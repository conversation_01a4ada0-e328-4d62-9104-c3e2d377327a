﻿using Ardalis.ListStartupServices;
using Microsoft.AspNetCore.Http.Features;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.Web.Middlewares;

namespace Witlab.Platform.Web.Configurations;

public static class MiddlewareConfig
{
  public static async Task<IApplicationBuilder> UseAppMiddlewareAndSeedDatabase(this WebApplication app)
  {
    if (app.Environment.IsDevelopment())
    {
      app.UseDeveloperExceptionPage();
      app.UseShowAllServicesMiddleware(); // see https://github.com/ardalis/AspNetCoreStartupServices
    }
    else
    {
      app.UseDefaultExceptionHandler(); // from FastEndpoints
      app.UseHsts();
    }

    app.UseCors("DefaultCorsPolicy");

    // 添加Session支持
    app.UseSession();

    // 添加认证和授权中间件
    app.UseAuthentication();
    app.UseAuthorization();

    // 添加会话跟踪中间件
    app.UseSessionTracking();

    // 注册响应中间件
    //app.UseUnifiedResponse();

    app.UseFastEndpoints(c =>
        {
          c.Endpoints.RoutePrefix = "api";
          //c.Endpoints.Configurator = ep =>
          //  {
          //    //if (ep.EndpointType.Name != nameof(Endpoints.WitLab.Proxy))
          //    //{
          //    //  //ep.ResponseInterceptor(new ApiResponseInterceptor());
          //    //  //ep.PostProcessors(Order.After, typeof(ApiResponsePostProcessor<,>)); 
          //    //}
          //  };
          //c.Endpoints.GlobalResponseModifier = async (ctx, content) => await ApiResponseModifier.ResponseModifiy(ctx,content);
        })
        .UseSwaggerGen()
        .UseResponseCaching(); // Includes AddFileServer and static files middleware

    app.UseHttpsRedirection(); // Note this will drop Authorization headers

    await SeedDatabase(app);

    return await Task.FromResult(app);
  }

  private static async Task SeedDatabase(WebApplication app)
  {
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;

    try
    {
      var context = services.GetRequiredService<AppDbContext>();
      //          context.Database.Migrate();
      context.Database.EnsureCreated();
      await SeedData.InitializeAsync(context);
    }
    catch (Exception ex)
    {
      var logger = services.GetRequiredService<ILogger<Program>>();
      logger.LogError(ex, "An error occurred seeding the DB. {exceptionMessage}", ex.Message);
    }
  }

  public static IServiceCollection AddMiddlewareConfigs(this IServiceCollection services, Microsoft.Extensions.Logging.ILogger logger, WebApplicationBuilder builder)
  {
    services.AddCors(o =>
      o.AddPolicy("DefaultCorsPolicy", p =>
        p.WithOrigins(origins: builder.Configuration.GetSection("AllowedHosts").Get<string>()?.Split(',') ?? ["*"])
         .AllowAnyMethod()
         .AllowAnyHeader()
      )
    );

    services.Configure<FormOptions>(options =>
    {
      options.MultipartBodyLengthLimit = 134217728; // Set a higher limit
    });

    //services.AddUnifiedResponse(options =>
    //{
    //  // 自定义封装格式
    //  options.EnvelopeFactory = (ctx, code, data, message, error) =>
    //      new { status = code, payload = data, note = message, detail = error };

    //  // 跳过静态资源流
    //  options.ShouldWrap = ctx => ctx.Response.ContentType != null &&
    //                              (ctx.Response.ContentType.StartsWith("application/json")
    //                               || ctx.Response.ContentType.StartsWith("text/"));
    //  // 自定义异常映射
    //  options.ExceptionToCode = ex =>
    //      ex is UnauthorizedAccessException ? 401 : 500;
    //  options.ExceptionToMessage = ex => ex.Message;
    //  options.BufferSize = 128 * 1024; // 正常场景buffer大小（可调）
    //});

    return services;
  }
}
