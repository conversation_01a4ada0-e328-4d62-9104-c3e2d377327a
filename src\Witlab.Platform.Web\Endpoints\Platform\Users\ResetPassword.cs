using Witlab.Platform.UseCases.Platform.Users.ResetPassword;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 重置用户密码
/// </summary>
[RequirePermission("user:reset-password")]
public class ResetPassword(IMediator _mediator) : Endpoint<ResetPasswordRequest>
{
  public override void Configure()
  {
    Post(ResetPasswordRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "重置用户密码";
      s.Description = "管理员重置指定用户的密码，无需提供旧密码";
      s.ExampleRequest = new ResetPasswordRequest { UserId = Guid.NewGuid(), NewPassword = "newpassword" };
    });
  }

  public override async Task HandleAsync(ResetPasswordRequest request, CancellationToken cancellationToken)
  {
    var command = new ResetPasswordCommand(
        request.UserId,
        request.NewPassword!
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
