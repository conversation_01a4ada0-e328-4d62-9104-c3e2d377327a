﻿using Witlab.Platform.UseCases.Platform.Depts.Create;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 创建部门
/// </summary>
[RequirePermission("dept:create")]
public class Create(IMediator _mediator) : Endpoint<CreateDeptRequest, CreateDeptResponse>
{
  public override void Configure()
  {
    Post(CreateDeptRequest.Route);
    Description(x => x.AutoTagOverride("Dept"));
    AllowAnonymous();
    Summary(s =>
        {
          s.Summary = "创建新部门";
          s.Description = "创建一个新部门，需要提供部门名称和编码";
          s.ExampleRequest = new CreateDeptRequest { DeptName = "研发部", DeptCode = "RD" };
        });
  }

  public override async Task HandleAsync(CreateDeptRequest request, CancellationToken cancellationToken)
  {
    var command = new CreateDeptCommand(
        request.DeptName!,
        request.DeptCode!,
        request.ParentId,
        request.Leader,
        request.OrderNum,
        request.Remark,
        request.Status == 1 ? true : false
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      //var dto = result.Value;
      //Response = new CreateDeptResponse(
      //    dto.Id,
      //    dto.DeptName,
      //    dto.DeptCode,
      //    dto.ParentId,
      //    dto.Leader,
      //    dto.OrderNum,
      //    dto.StateValue,
      //    dto.StateName,
      //    dto.Remark
      //);
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync();
      return;
    }

    await SendErrorsAsync();
  }
}
