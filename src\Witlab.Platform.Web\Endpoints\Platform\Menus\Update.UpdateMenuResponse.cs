﻿namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

public class UpdateMenuResponse
{
  public UpdateMenuResponse(
      Guid id,
      string menuName,
      Guid? parentId,
      int menuTypeValue,
      string menuTypeName,
      int orderNum,
      int stateValue,
      string stateName,
      string? icon,
      string? activeIcon,
      string? router,
      string? routerName,
      string? component,
      string? query,
      string? remark,
      string? title,
      bool affixTab,
      int? affixTabOrder,
      string? badge,
      string? badgeType,
      string? badgeVariants,
      string? iframeSrc,
      string? link,
      bool openInNewWindow,
      bool keepAlive,
      bool hideInMenu,
      bool hideInTab,
      bool hideInBreadcrumb,
      bool hideChildrenInMenu,
      string? activePath,
      int? maxNumOfOpenTab,
      bool? noBasicLayout
      )
  {
    Id = id;
    MenuName = menuName;
    ParentId = parentId;
    MenuTypeValue = menuTypeValue;
    MenuTypeName = menuTypeName;
    OrderNum = orderNum;
    StateValue = stateValue;
    StateName = stateName;
    Icon = icon;
    ActiveIcon = activeIcon;
    Router = router;
    RouterName = routerName;
    Component = component;
    Query = query;
    Remark = remark;
    Title = title;
    AffixTab = affixTab;
    AffixTabOrder = affixTabOrder;
    Badge = badge;
    BadgeType = badgeType;
    BadgeVariants = badgeVariants;
    IframeSrc = iframeSrc;
    Link = link;
    OpenInNewWindow = openInNewWindow;
    KeepAlive = keepAlive;
    HideInMenu = hideInMenu;
    HideInTab = hideInTab;
    HideInBreadcrumb = hideInBreadcrumb;
    HideChildrenInMenu = hideChildrenInMenu;
    ActivePath = activePath;
    MaxNumofOpenTab = maxNumOfOpenTab;
    NoBasicLayout = noBasicLayout;
  }

  public Guid Id { get; set; }
  public string MenuName { get; set; }
  public Guid? ParentId { get; set; }
  public int MenuTypeValue { get; set; }
  public string MenuTypeName { get; set; }
  public int OrderNum { get; set; }
  public int StateValue { get; set; }
  public string StateName { get; set; }
  public string? Icon { get; set; }
  public string? ActiveIcon { get; set; }
  public string? Router { get; set; }
  public string? RouterName { get; set; }
  public string? Component { get; set; }
  public string? Query { get; set; }
  public string? Remark { get; set; }
  public string? Title { get; set; }
  public bool AffixTab { get; set; }
  public int? AffixTabOrder { get; set; }
  public string? Badge { get; set; }
  public string? BadgeType { get; set; }
  public string? BadgeVariants { get; set; }
  public string? IframeSrc { get; set; }
  public string? Link { get; set; }
  public bool OpenInNewWindow { get; set; }
  public bool KeepAlive { get; set; }
  public bool HideInMenu { get; set; }
  public bool HideInTab { get; set; }
  public bool HideInBreadcrumb { get; set; }
  public bool HideChildrenInMenu { get; set; }
  public string? ActivePath { get; set; }
  public int? MaxNumofOpenTab { get; set; }
  public bool? NoBasicLayout { get; set; }
}
