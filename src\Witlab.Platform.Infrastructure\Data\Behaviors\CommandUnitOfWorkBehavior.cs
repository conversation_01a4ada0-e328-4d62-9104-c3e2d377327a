﻿using MediatR;

namespace Witlab.Platform.Infrastructure.Data.Behaviors;

public class CommandUnitOfWorkBehavior<TCommand, TResponse> : IPipelineBehavior<TCommand, TResponse>
    where TCommand : ICommand<TCommand>
{
  private readonly ILogger<CommandUnitOfWorkBehavior<TCommand, TResponse>> _logger;
  private readonly ITransactionUnitOfWork _unitOfWork;

  public CommandUnitOfWorkBehavior(ILogger<CommandUnitOfWorkBehavior<TCommand, TResponse>> logger, ITransactionUnitOfWork unitOfWork)
  {
    _logger = logger;
    _unitOfWork = unitOfWork;
  }


  public async Task<TResponse> Handle(TCommand request, RequestHandlerDelegate<TResponse> next,
      CancellationToken cancellationToken)
  {
    var cmdType = typeof(TCommand);
    var commandName = cmdType.FullName ?? cmdType.Name;
    var id = Guid.NewGuid();
    if (_unitOfWork.CurrentTransaction != null)
    {
      try
      {
        _logger.LogInformation("CommandUnitOfWorkBehavior: Command Begin: {CommandName}, Id: {Id}", commandName, id);
        var response = await next();
        _logger.LogInformation("CommandUnitOfWorkBehavior: Command End: {CommandName}, Id: {Id}", commandName, id);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return response;
      }
      catch (Exception e)
      {
        _logger.LogError("CommandUnitOfWorkBehavior: Command Error: {CommandName}, Id: {Id}, Exception: {ExceptionMessage}", commandName, id, e.Message);
        throw;
      }
    }


    await using var transaction = await _unitOfWork.BeginTransactionAsync(cancellationToken);
    _logger.LogInformation("CommandUnitOfWorkBehavior: TransactionBegin: TransactionId: {TransactionId}", transaction.TransactionId);
    // 此处为了支持 async 开启事务，同时使得CAP组件正常工作，所以需要手动设置CurrentTransaction，以确保事务提交之前，CAP事件不会被发出
    // see: https://github.com/dotnetcore/CAP/issues/1656
    _unitOfWork.CurrentTransaction = transaction;
    await using (_unitOfWork.CurrentTransaction!)
    {
      try
      {
        _logger.LogInformation("CommandUnitOfWorkBehavior: Command Begin: {CommandName}, Id: {Id}", commandName, id);
        var response = await next();
        _logger.LogInformation("CommandUnitOfWorkBehavior: Command End: {CommandName}, Id: {Id}", commandName, id);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        await _unitOfWork.CommitAsync(cancellationToken);
        _logger.LogInformation("CommandUnitOfWorkBehavior: TransactionCommit: TransactionId: {TransactionId}", transaction.TransactionId);
        return response;
      }
      catch (Exception e)
      {
        _logger.LogError("CommandUnitOfWorkBehavior: Command Error: {CommandName}, Id: {Id}, Exception: {ExceptionMessage}", commandName, id, e.Message);
        await _unitOfWork.RollbackAsync(cancellationToken);
        _logger.LogInformation("CommandUnitOfWorkBehavior: TransactionRollback: TransactionId: {TransactionId}", transaction.TransactionId);
        throw;
      }
    }
  }
}
