﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

public class ResetPasswordRequest
{
  public const string Route = "/platform/users/{UserId:guid}/reset-password";
  public static string BuildRoute(Guid userId) => Route.Replace("{UserId:guid}", userId.ToString());

  public Guid UserId { get; set; }

  [Required]
  [StringLength(100, MinimumLength = 6)]
  public string? NewPassword { get; set; }
}
