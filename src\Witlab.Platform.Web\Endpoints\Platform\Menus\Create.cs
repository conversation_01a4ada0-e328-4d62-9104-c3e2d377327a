﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Menus.Create;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 创建菜单
/// </summary>
[RequirePermission("menu:create")]
public class Create(IMediator _mediator) : Endpoint<CreateMenuRequest>
{
  public override void Configure()
  {
    Post(CreateMenuRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    AllowAnonymous();
    Summary(s =>
        {
          s.Summary = "创建新菜单";
          s.Description = "创建一个新菜单，需要提供菜单名称和类型";
          s.ExampleRequest = new CreateMenuRequest { Name = "系统管理" };
        });
  }

  public override async Task HandleAsync(CreateMenuRequest request, CancellationToken cancellationToken)
  {
    var menuType = MenuType.FromName(request.Type, true);

    var command = new CreateMenuCommand(
        request.Name,
        menuType,
        request.Pid,
        request.Meta.Order,
        request.Meta.Icon,
        request.Meta.ActiveIcon,
        request.Path,
        request.Component,
        request.Name,
        request.Redirect,
        request.Meta.Query,
        null, // remark
        request.Meta.Title,
        request.Meta.AffixTab,
        request.Meta.AffixTabOrder,
        request.Meta.Badge,
        request.Meta.BadgeType,
        request.Meta.BadgeVariants,
        request.Meta.IframeSrc,
        request.Meta.Link,
        request.Meta.OpenInNewWindow,
        request.Meta.KeepAlive,
        request.Meta.HideInMenu,
        request.Meta.HideInTab,
        request.Meta.HideInBreadcrumb,
        request.Meta.HideChildrenInMenu,
        request.Meta.ActivePath,
        request.Meta.MaxNumofOpenTab,
        request.Meta.NoBasicLayout,
        request.AuthCode
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
