﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 用户角色关系表
///</summary>
public class UserRole : EntityBase<Guid>
{
  private UserRole()
  {

  }
  public UserRole(Guid userId, Guid roleId)
  {
    UserId = userId;
    RoleId = roleId;
  }

  /// <summary>
  /// 角色id
  /// </summary>
  public Guid RoleId { get; init; }
  public Role Role { get; set; } = null!;

  /// <summary>
  /// 用户id
  /// </summary>
  public Guid UserId { get; init; }
  public User User { get; set; } = null!;
}
