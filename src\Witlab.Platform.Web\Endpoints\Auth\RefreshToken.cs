﻿using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 刷新令牌接口
/// </summary>
public class RefreshToken(IAuthService _authService) : Endpoint<RefreshTokenRequest, LoginResponse>
{
  public override void Configure()
  {
    Post("/auth/refresh-token");
    Description(x => x.WithTags("Auth"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "刷新令牌";
      s.Description = "使用刷新令牌获取新的访问令牌";
    });
  }

  public override async Task HandleAsync(RefreshTokenRequest request, CancellationToken cancellationToken)
  {
    var result = await _authService.RefreshTokenAsync(request.AccessToken!, request.RefreshToken!, request.DeptCode, request.RoleCode);

    if (result.IsSuccess)
    {
      var authResponse = result.Value;
      Response = new LoginResponse
      {
        AccessToken = authResponse.AccessToken,
        RefreshToken = authResponse.RefreshToken,
        ExpiresIn = authResponse.ExpiresIn,
        TokenType = authResponse.TokenType,
        UserId = authResponse.UserId,
        UserName = authResponse.UserName
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }
    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
