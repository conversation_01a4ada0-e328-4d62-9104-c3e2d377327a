﻿using Witlab.Platform.Infrastructure.WitLab;
using Witlab.Platform.Infrastructure.WitLab.Extensions;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Web.Endpoints.WitLab;

public class UploadFileEndpoint(IWitLabFileService _witLabFileService) : EndpointWithoutRequest
{
  private static readonly HashSet<string> HopByHopHeaders = new(StringComparer.OrdinalIgnoreCase)
  {
      "Connection", "Keep-Alive", "Proxy-Authenticate", "Proxy-Authorization",
      "TE", "Trailers", "Transfer-Encoding", "Upgrade"
  };

  public override void Configure()
  {
    Post("/WitLab/Files/{*path}");
    Description(x => x.AutoTagOverride("WitLab"));
    AllowFileUploads(); // 允许文件上传
    AllowAnonymous();   // 可根据需要设置身份验证
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    if (Files.Count == 0)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }
    //var file = Files;

    var path = Route<string?>("path");

    var uploadResponseResult = await _witLabFileService.UploadFileAsync(HttpContext.Request, path, cancellationToken);

    if (uploadResponseResult.IsSuccess)
    {
      var uploadResponse = uploadResponseResult.Value;
      var response = await uploadResponse.ToWitLabApiResponse(cancellationToken);
      await HandleResponseAsync(response, cancellationToken);
    }
    else
    {
      await SendAsync(uploadResponseResult.Errors, (int)System.Net.HttpStatusCode.InternalServerError);
    }
  }

  private async Task HandleResponseAsync(WitLabApiResponse response, CancellationToken cancellationToken = default)
  {
    // 复制响应头
    foreach (var header in response.Headers)
    {
      if (!HopByHopHeaders.Contains(header.Key))
      {
        HttpContext.Response.Headers[header.Key] = header.Value.ToArray();
      }
    }

    try
    {
      var result = new
      {
        Code = response.StatusCode,
        Data = new
        {
          response.Result,
          response.Success
        },
        response.Message,
        Error = string.Empty
      };
      await SendAsync(result, statusCode: response.StatusCode, cancellationToken);
      return;
    }
    catch (Exception ex)
    {
      await SendAsync(new
      {
        Code = response.StatusCode,
        Data = new
        {
          response.Result,
          response.Success
        },
        response.Message,
        Error = ex.Message
      },
      statusCode: StatusCodes.Status500InternalServerError,
      cancellationToken);
    }
  }
}
