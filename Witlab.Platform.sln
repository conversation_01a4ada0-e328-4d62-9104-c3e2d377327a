﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31815.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{106AE906-5075-410A-B941-912F811848EE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{B31B4797-1D9F-4288-808C-BE9A31A98C7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.Web", "src\Witlab.Platform.Web\Witlab.Platform.Web.csproj", "{C9751CB7-4CD6-4633-A99A-4F6ADD525437}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.Core", "src\Witlab.Platform.Core\Witlab.Platform.Core.csproj", "{4548EB20-1D1B-477B-AF6E-DD55653A6583}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.Infrastructure", "src\Witlab.Platform.Infrastructure\Witlab.Platform.Infrastructure.csproj", "{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{250F283E-FE2F-4BBD-9E63-A2265B84E23F}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		CleanArchitecture.nuspec = CleanArchitecture.nuspec
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.FunctionalTests", "tests\Witlab.Platform.FunctionalTests\Witlab.Platform.FunctionalTests.csproj", "{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}"
	ProjectSection(ProjectDependencies) = postProject
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437} = {C9751CB7-4CD6-4633-A99A-4F6ADD525437}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.IntegrationTests", "tests\Witlab.Platform.IntegrationTests\Witlab.Platform.IntegrationTests.csproj", "{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}"
	ProjectSection(ProjectDependencies) = postProject
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D} = {220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.UnitTests", "tests\Witlab.Platform.UnitTests\Witlab.Platform.UnitTests.csproj", "{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.UseCases", "src\Witlab.Platform.UseCases\Witlab.Platform.UseCases.csproj", "{B74A78FF-B79E-4C38-A9C7-084A90990CAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.ServiceDefaults", "src\Witlab.Platform.ServiceDefaults\Witlab.Platform.ServiceDefaults.csproj", "{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Witlab.Platform.AspireHost", "src\Witlab.Platform.AspireHost\Witlab.Platform.AspireHost.csproj", "{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Witlab.Platform.AspireTests", "tests\Witlab.Platform.AspireTests\Witlab.Platform.AspireTests.csproj", "{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WitLab.Platform.SharedKernel", "src\WitLab.Platform.SharedKernel\WitLab.Platform.SharedKernel.csproj", "{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|x64.Build.0 = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Debug|x86.Build.0 = Debug|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|x64.ActiveCfg = Release|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|x64.Build.0 = Release|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|x86.ActiveCfg = Release|Any CPU
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437}.Release|x86.Build.0 = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|x64.Build.0 = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Debug|x86.Build.0 = Debug|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|Any CPU.Build.0 = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|x64.ActiveCfg = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|x64.Build.0 = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|x86.ActiveCfg = Release|Any CPU
		{4548EB20-1D1B-477B-AF6E-DD55653A6583}.Release|x86.Build.0 = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|x64.Build.0 = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Debug|x86.Build.0 = Debug|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|x64.ActiveCfg = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|x64.Build.0 = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|x86.ActiveCfg = Release|Any CPU
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D}.Release|x86.Build.0 = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|x64.Build.0 = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Debug|x86.Build.0 = Debug|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|x64.ActiveCfg = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|x64.Build.0 = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|x86.ActiveCfg = Release|Any CPU
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C}.Release|x86.Build.0 = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|x64.Build.0 = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Debug|x86.Build.0 = Debug|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|Any CPU.Build.0 = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|x64.ActiveCfg = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|x64.Build.0 = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|x86.ActiveCfg = Release|Any CPU
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17}.Release|x86.Build.0 = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|x64.Build.0 = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Debug|x86.Build.0 = Debug|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|x64.ActiveCfg = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|x64.Build.0 = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|x86.ActiveCfg = Release|Any CPU
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5}.Release|x86.Build.0 = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|x64.Build.0 = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Debug|x86.Build.0 = Debug|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|x64.ActiveCfg = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|x64.Build.0 = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|x86.ActiveCfg = Release|Any CPU
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD}.Release|x86.Build.0 = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|x64.Build.0 = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Debug|x86.Build.0 = Debug|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|x64.ActiveCfg = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|x64.Build.0 = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|x86.ActiveCfg = Release|Any CPU
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2}.Release|x86.Build.0 = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|x64.Build.0 = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Debug|x86.Build.0 = Debug|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|x64.ActiveCfg = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|x64.Build.0 = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|x86.ActiveCfg = Release|Any CPU
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870}.Release|x86.Build.0 = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|x64.Build.0 = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Debug|x86.Build.0 = Debug|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|Any CPU.Build.0 = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|x64.ActiveCfg = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|x64.Build.0 = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|x86.ActiveCfg = Release|Any CPU
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF}.Release|x86.Build.0 = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|x64.Build.0 = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Debug|x86.Build.0 = Debug|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|x64.ActiveCfg = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|x64.Build.0 = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|x86.ActiveCfg = Release|Any CPU
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C9751CB7-4CD6-4633-A99A-4F6ADD525437} = {106AE906-5075-410A-B941-912F811848EE}
		{4548EB20-1D1B-477B-AF6E-DD55653A6583} = {106AE906-5075-410A-B941-912F811848EE}
		{220361D6-9C76-4E3F-BD34-3C7B50E2CA4D} = {106AE906-5075-410A-B941-912F811848EE}
		{7D84EFEE-A7D9-44AD-A0A3-38EC7882D94C} = {B31B4797-1D9F-4288-808C-BE9A31A98C7D}
		{0776DC14-9000-47A4-A3F4-ECBCF8CEBC17} = {B31B4797-1D9F-4288-808C-BE9A31A98C7D}
		{1DC7F5A0-DDF7-4975-84EB-05F4FC1B6AB5} = {B31B4797-1D9F-4288-808C-BE9A31A98C7D}
		{B74A78FF-B79E-4C38-A9C7-084A90990CAD} = {106AE906-5075-410A-B941-912F811848EE}
		{08E69B3B-4418-40BD-80EC-B38C0ECFBAE2} = {106AE906-5075-410A-B941-912F811848EE}
		{C7039CB5-4F76-4F19-ABD7-C755FAB2A870} = {106AE906-5075-410A-B941-912F811848EE}
		{07D864BA-6245-D5C0-5C79-BB55D27ACBBF} = {B31B4797-1D9F-4288-808C-BE9A31A98C7D}
		{3E743A06-E06C-18C5-D8AE-CACF12E7A57D} = {106AE906-5075-410A-B941-912F811848EE}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B0F19343-8185-4A9F-9165-0EA8544BC925}
	EndGlobalSection
EndGlobal
