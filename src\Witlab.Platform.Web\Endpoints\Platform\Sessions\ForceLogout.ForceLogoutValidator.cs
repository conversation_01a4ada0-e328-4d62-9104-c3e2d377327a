﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 强制下线请求验证器
/// </summary>
public class ForceLogoutValidator : Validator<ForceLogoutRequest>
{
  public ForceLogoutValidator()
  {
    RuleFor(x => x.SessionId)
      .NotEmpty()
      .WithMessage("会话ID不能为空");

    RuleFor(x => x.Reason)
      .MaximumLength(500)
      .WithMessage("下线原因不能超过500个字符");
  }
}

/// <summary>
/// 强制下线用户所有会话请求验证器
/// </summary>
public class ForceLogoutAllUserSessionsValidator : Validator<ForceLogoutAllUserSessionsRequest>
{
  public ForceLogoutAllUserSessionsValidator()
  {
    RuleFor(x => x.UserId)
      .NotEmpty()
      .WithMessage("用户ID不能为空");

    RuleFor(x => x.Reason)
      .MaximumLength(500)
      .WithMessage("下线原因不能超过500个字符");
  }
}
