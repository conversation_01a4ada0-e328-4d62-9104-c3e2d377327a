﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.Update;

/// <summary>
/// 更新角色命令处理器
/// </summary>
public class UpdateRoleHandler : ICommandHandler<UpdateRoleCommand, Result<RoleDTO>>
{
  private readonly IRoleService _roleService;

  public UpdateRoleHandler(IRoleService roleService)
  {
    _roleService = roleService;
  }

  public async Task<Result<RoleDTO>> Handle(UpdateRoleCommand request, CancellationToken cancellationToken)
  {
    var result = await _roleService.UpdateRoleAsync(
        request.RoleId,
        request.RoleName,
        request.RoleCode,
        request.Permissions,
        request.Remark,
        request.Status
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var role = result.Value;
    return Result.Success(MapToDto(role));
  }

  private static RoleDTO MapToDto(Role role)
  {
    return new RoleDTO(
        role.Id,
        role.RoleName,
        role.RoleCode,
        role.Remark,
        role.State,
        role.Permissions?.Select(p => p.Code).ToArray() ?? Array.Empty<string>(),
        role.Created.DateTime,
        role.CreatedBy,
        role.LastModified.DateTime,
        role.LastModifiedBy
    );
  }
}
