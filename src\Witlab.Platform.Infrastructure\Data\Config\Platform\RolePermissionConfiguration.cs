﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class RolePermissionConfiguration : IEntityTypeConfiguration<RolePermission>
{
  public void Configure(EntityTypeBuilder<RolePermission> builder)
  {
    // 将主键设置为 RoleId 和 PermissionId 的复合主键
    builder.HasKey(rp => new { rp.RoleId, rp.PermissionId });

    builder.Property(p => p.RoleId)
        .IsRequired();

    builder.Property(p => p.PermissionId)
        .IsRequired();

    // 配置与 Role 实体的关系
    builder.HasOne(rp => rp.Role) // 使用 RolePermission.Role 导航属性
        .WithMany(r => r.RolePermissions) // 使用 Role.RolePermissions 集合属性
        .HasForeignKey(rp => rp.RoleId)
        .OnDelete(DeleteBehavior.Cascade);

    // 配置与 Permission 实体的关系
    builder.HasOne(rp => rp.Permission) // 使用 RolePermission.Permission 导航属性
        .WithMany(p => p.RolePermissions) // 使用 Permission.RolePermissions 集合属性
        .HasForeignKey(rp => rp.PermissionId)
        .OnDelete(DeleteBehavior.Cascade);

    // 由于 (RoleId, PermissionId) 已是复合主键，不再需要单独的唯一索引
    // builder.HasIndex(rp => new { rp.RoleId, rp.PermissionId })
    // .IsUnique();

    // ToTable 可以在 RoleConfiguration 或 PermissionConfiguration 中定义，这里移除
    // builder.ToTable("RolePermissions");
  }
}
