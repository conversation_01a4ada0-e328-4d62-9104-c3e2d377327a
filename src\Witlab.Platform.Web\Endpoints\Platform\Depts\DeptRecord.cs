﻿using System.Text.Json.Serialization;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

public class DeptRecord
{
  [JsonPropertyName("id")]
  public Guid? DeptId { get; set; }
  [JsonPropertyName("name")]
  public required string DeptName { get; set; }
  [JsonPropertyName("code")]
  public required string DeptCode { get; set; }
  [JsonPropertyName("pid")]
  public Guid? ParentId { get; set; }
  public List<DeptRecord>? Children { get; set; }
  public string? Leader { get; set; }
  public int OrderNum { get; set; }
  [JsonPropertyName("status")]
  public int Status { get; set; }
  public string? Remark { get; set; }
  [JsonPropertyName("createTime")]
  public DateTime CreatedOnUtc { get; set; }
  public string? CreatedBy { get; set; }
  [JsonPropertyName("LastModifiedTime")]
  public DateTime? LastModifiedOnUtc { get; set; }
  public string? LastModifiedBy { get; set; }
}
