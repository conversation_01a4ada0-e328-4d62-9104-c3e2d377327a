﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 分配角色给用户请求验证器
/// </summary>
public class AssignRolesToUserValidator : Validator<AssignRolesToUserRequest>
{
  public AssignRolesToUserValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.RoleIds)
        .NotNull().WithMessage("角色ID列表不能为空");
  }
}
