﻿using Microsoft.AspNetCore.Http.Extensions;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Web.Endpoints.WitLab;

public class DownloadFileEndpoint(IWitLabFileService _witLabFileService) : EndpointWithoutRequest
{
  public override void Configure()
  {
    Get("/WitLab/Files/{*path}");
    Description(x => x.AutoTagOverride("WitLab"));
    AllowAnonymous();   // 可根据需要设置身份验证
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var path = Route<string?>("path", isRequired: true);
    if (string.IsNullOrEmpty(path))
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    var queryBuilder = new QueryBuilder(HttpContext.Request.Query.ToList());

    // QueryString
    var qsString = queryBuilder.ToQueryString().Value;

    var downloadResponseResult = await _witLabFileService.DownloadFileAsync(HttpContext.Request, path + qsString, cancellationToken);

    if (!downloadResponseResult.IsSuccess)
    {
      await SendAsync(downloadResponseResult.Errors, (int)System.Net.HttpStatusCode.InternalServerError, cancellationToken);
    }

    var downloadResponse = downloadResponseResult.Value;

    if (!downloadResponse.IsSuccessStatusCode)
    {
      await SendAsync(downloadResponse.ReasonPhrase, (int)System.Net.HttpStatusCode.InternalServerError, cancellationToken);
    }

    var stream = await downloadResponse.Content.ReadAsStreamAsync(cancellationToken);
    var fileName = downloadResponse.Content.Headers.ContentDisposition?.FileNameStar
                   ?? downloadResponse.Content.Headers.ContentDisposition?.FileName;

    HttpContext.Response.Headers.Append("Access-Control-Expose-Headers", "Content-Disposition");

    await SendStreamAsync(
        stream,
        fileName: fileName,
        cancellation: cancellationToken
    );

  }

}
