﻿using Witlab.Platform.UseCases.Platform.Users.Delete;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 删除用户
/// </summary>
public class Delete(IMediator _mediator) : Endpoint<DeleteUserRequest>
{
  public override void Configure()
  {
    Delete(DeleteUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "删除用户";
      s.Description = "根据用户ID删除用户";
    });
  }

  public override async Task HandleAsync(DeleteUserRequest request, CancellationToken cancellationToken)
  {
    var command = new DeleteUserCommand(request.UserId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
