﻿using Witlab.Platform.UseCases.Platform.Permissions.GetUserPermissions;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 获取用户角色
/// </summary>
public class GetUserPermissions(IMediator _mediator) : Endpoint<GetUserPermissionsRequest, List<PermissionRecord>>
{
  public override void Configure()
  {
    Get(GetUserPermissionsRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "获取用户角色";
      s.Description = "获取指定用户的所有角色";
    });
  }

  public override async Task HandleAsync(GetUserPermissionsRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserPermissionsQuery(request.UserId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var roles = result.Value.Select(dto => new PermissionRecord(
          dto.Id,
          dto.Code,
          dto.Name,
          dto.Description,
          dto.TypeValue,
          dto.TypeName,
          dto.IsEnabled,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
        )).ToList();

      Response = roles;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
