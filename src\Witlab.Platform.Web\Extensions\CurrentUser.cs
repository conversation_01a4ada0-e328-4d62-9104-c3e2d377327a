﻿using System.Security.Claims;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Web.Extensions;
public class CurrentUser : IUser
{
  private readonly IHttpContextAccessor _httpContextAccessor;

  public CurrentUser(IHttpContextAccessor httpContextAccessor)
  {
    _httpContextAccessor = httpContextAccessor;
  }

  public string? Id => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);

  /// <summary>
  /// 获取当前用户名
  /// </summary>
  public string? UserName => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);

  /// <summary>
  /// 获取当前用户角色
  /// </summary>
  public IEnumerable<string> Roles
  {
    get
    {
      var user = _httpContextAccessor.HttpContext?.User;
      if (user == null)
      {
        return Enumerable.Empty<string>();
      }

      return user.Claims
          .Where(c => c.Type == ClaimTypes.Role)
          .Select(c => c.Value);
    }
  }

  public string? DeptCode => _httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == $"Current{nameof(Dept.DeptCode)}")?.Value;

  public string? RoleCode => _httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == $"Current{nameof(Role.RoleCode)}")?.Value;

  /// <summary>
  /// 判断当前用户是否拥有指定角色
  /// </summary>
  /// <param name="role">角色</param>
  /// <returns>是否拥有</returns>
  public bool IsInRole(string role)
  {
    return Roles.Any(r => r.Equals(role, StringComparison.OrdinalIgnoreCase));
  }
}
