﻿using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.UseCases.WitLab.LabAccessKeys.Get;

/// <summary>
/// 获取菜单查询处理器
/// </summary>
public class GetAccessKeysHandler : I<PERSON>ueryHandler<GetAccessKeysQuery, Result<LabAccessKeysDTO>>
{
  private readonly ILabAccessKeysService _accessKeysService;

  public GetAccessKeysHandler(ILabAccessKeysService accessKeysService)
  {
    _accessKeysService = accessKeysService;
  }

  public async Task<Result<LabAccessKeysDTO>> Handle(GetAccessKeysQuery request, CancellationToken cancellationToken)
  {
    var result = await _accessKeysService.GetAccessKeys(request.UserName, request.DeptCode, request.RoleCode);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var keys = result.Value;
    return Result.Success(new LabAccessKeysDTO(keys.UserName, keys.DeptCode, keys.RoleCode, keys.AccessKey, keys.SecretKey, keys.State.Value, keys.State.Name));
  }
}
