﻿using Witlab.Platform.UseCases.Platform.Roles.GetUserRoles;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 获取用户角色
/// </summary>
public class GetUserRoles(IMediator _mediator) : Endpoint<GetUserRolesRequest, List<RoleRecord>>
{
  public override void Configure()
  {
    Get(GetUserRolesRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "获取用户角色";
      s.Description = "获取指定用户的所有角色";
    });
  }

  public override async Task HandleAsync(GetUserRolesRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserRolesQuery(request.UserId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var roles = result.Value.Select(dto => new RoleRecord()
      {
        Id = dto.Id.ToString(),
        RoleName = dto.RoleName,
        RoleCode = dto.RoleCode,
        Remark = dto.Remark,
        Status = dto.State ? 1 : 0,
        Permissions = dto.Permissions.ToList(),
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      }).ToList();

      Response = roles;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
