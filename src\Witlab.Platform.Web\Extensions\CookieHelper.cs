﻿using System.Text.Json;

namespace Witlab.Platform.Web.Extensions;

/// <summary>
/// Cookie 操作帮助类
/// </summary>
public static class CookieHelper
{
  /// <summary>
  /// 设置 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key"><PERSON>ie 键</param>
  /// <param name="value"><PERSON>ie 值</param>
  /// <param name="expires">过期时间（分钟）</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void SetCookie(
      HttpContext context,
      string key,
      string value,
      int? expires = null,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    var options = new CookieOptions
    {
      HttpOnly = httpOnly,
      Secure = secure,
      SameSite = sameSite
    };

    if (expires.HasValue)
    {
      options.Expires = DateTime.Now.AddMinutes(expires.Value);
    }

    context.Response.Cookies.Append(key, value, options);
  }

  /// <summary>
  /// 获取 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <returns>Cookie 值</returns>
  public static string? GetCookie(HttpContext context, string key)
  {
    if (context.Request.Cookies.TryGetValue(key, out var value))
    {
      return value;
    }
    return null;
  }

  /// <summary>
  /// 删除 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void DeleteCookie(
      HttpContext context,
      string key,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    var options = new CookieOptions
    {
      HttpOnly = httpOnly,
      Secure = secure,
      SameSite = sameSite,
      Expires = DateTime.Now.AddDays(-1) // 设置为过期
    };

    context.Response.Cookies.Append(key, "", options);
  }

  /// <summary>
  /// 设置认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="token">令牌值</param>
  /// <param name="expires">过期时间（分钟）</param>
  public static void SetAuthTokenCookie(
      HttpContext context,
      string token,
      int expires = 1440) // 默认24小时 (24 * 60 = 1440分钟)
  {
    SetCookie(
        context,
        "jwt",
        token,
        expires,
        httpOnly: true,
        secure: true,
        sameSite: SameSiteMode.None
    );
  }

  /// <summary>
  /// 获取认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <returns>令牌值</returns>
  public static string? GetAuthTokenCookie(HttpContext context)
  {
    return GetCookie(context, "jwt");
  }

  /// <summary>
  /// 清除认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  public static void ClearAuthTokenCookie(HttpContext context)
  {
    DeleteCookie(
        context,
        "jwt",
        httpOnly: true,
        secure: true,
        sameSite: SameSiteMode.None
    );
  }

  /// <summary>
  /// 设置对象到 Cookie
  /// </summary>
  /// <typeparam name="T">对象类型</typeparam>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <param name="value">对象值</param>
  /// <param name="expires">过期时间（分钟）</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void SetObjectCookie<T>(
      HttpContext context,
      string key,
      T value,
      int? expires = null,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    if (value == null)
    {
      DeleteCookie(context, key, httpOnly, secure, sameSite);
      return;
    }

    var jsonString = JsonSerializer.Serialize(value);
    SetCookie(context, key, jsonString, expires, httpOnly, secure, sameSite);
  }

  /// <summary>
  /// 从 Cookie 获取对象
  /// </summary>
  /// <typeparam name="T">对象类型</typeparam>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <returns>对象值</returns>
  public static T? GetObjectCookie<T>(HttpContext context, string key)
  {
    var jsonString = GetCookie(context, key);
    if (string.IsNullOrEmpty(jsonString))
    {
      return default;
    }

    try
    {
      return JsonSerializer.Deserialize<T>(jsonString);
    }
    catch
    {
      // 如果反序列化失败，删除无效的 Cookie
      DeleteCookie(context, key);
      return default;
    }
  }

  /// <summary>
  /// 获取所有 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <returns>Cookie 键值对字典</returns>
  public static Dictionary<string, string> GetAllCookies(HttpContext context)
  {
    var result = new Dictionary<string, string>();
    foreach (var cookie in context.Request.Cookies)
    {
      result.Add(cookie.Key, cookie.Value);
    }
    return result;
  }

  /// <summary>
  /// 检查 Cookie 是否存在
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <returns>是否存在</returns>
  public static bool CookieExists(HttpContext context, string key)
  {
    return context.Request.Cookies.ContainsKey(key);
  }
}
