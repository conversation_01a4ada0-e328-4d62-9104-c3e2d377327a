﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Deactivate;

/// <summary>
/// 禁用用户命令处理器
/// </summary>
public class DeactivateUserHandler : ICommandHandler<DeactivateUserCommand, Result>
{
  private readonly IUserService _userService;

  public DeactivateUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(DeactivateUserCommand request, CancellationToken cancellationToken)
  {
    return await _userService.DeactivateUserAsync(request.UserId);
  }
}
