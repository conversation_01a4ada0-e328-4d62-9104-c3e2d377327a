﻿using System.Net.WebSockets;
using System.Text.Json;
using Ardalis.Result;
using MediatR;
using Microsoft.Extensions.Caching.Distributed;
using Witlab.Platform.Infrastructure.WitLab.Configurations;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;
using Witlab.Platform.UseCases.WitLab.LabAccessKeys.Get;
using Witlab.Platform.UseCases.WitLab.LabAccessKeys.UpdateLabAccessKeys;

namespace Witlab.Platform.Infrastructure.WitLab.Services;

public record AccessKeysCacheItem(string AccessKey, string SecretKey);

public class WitLabKeyProvider : IWitLabKeyProvider
{
  private readonly ILogger<WitLabKeyProvider> _logger;
  private readonly IDistributedCache _cache;
  private readonly IMediator _mediator;
  private readonly IOptionsSnapshot<WitLabConfiguration> _configuration;
  private readonly IServiceProvider _serviceProvider;
  private const string CACHE_KEY_PREFIX = "WitLab_UserKeys_";

  public WitLabKeyProvider(
      ILogger<WitLabKeyProvider> logger,
      IDistributedCache cache,
      IMediator mediator,
      IOptionsSnapshot<WitLabConfiguration> configuration,
      IServiceProvider serviceProvider)
  {
    _logger = logger;
    _cache = cache;
    _mediator = mediator;
    _configuration = configuration;
    _serviceProvider = serviceProvider;
  }

  public async Task<(string AccessKey, string SecretKey)> GetUserKeysAsync(string? userName, string? deptCode, string? roleCode)
  {
    string? ak = null;
    string? sk = null;
    if (!string.IsNullOrEmpty(userName) && !string.IsNullOrEmpty(deptCode))
    {
      // Attempt to retrieve keys from cache
      var cacheKey = $"{CACHE_KEY_PREFIX}{userName}_{deptCode}";
      if (!string.IsNullOrEmpty(roleCode))
      {
        cacheKey += $"_{roleCode}";
      }

      var cachedData = await _cache.GetStringAsync(cacheKey);
      if (cachedData != null)
      {
        var keys = JsonSerializer.Deserialize<AccessKeysCacheItem>(cachedData);
        if (keys != null && keys.SecretKey != null && keys.AccessKey != null)
        {
          return (keys.AccessKey, keys.SecretKey);
        }
      }

      // Fetch keys from the database or create them if they do not exist
      var storedKeysResult = await _mediator.Send(new GetAccessKeysQuery(userName, deptCode, roleCode));
      if (storedKeysResult.IsSuccess)
      {
        var fetchKeys = storedKeysResult.Value;
        ak = fetchKeys.AccessKey;
        sk = fetchKeys.SecretKey;
      }
      else
      {
        // If fetching keys fails, create or update them
        var fetchKeysResult = await fetchUserKeys(userName, deptCode, roleCode);
        var fetchKeys = fetchKeysResult.Value;

        var updateKeysResult = await _mediator.Send(new UpdateLabAccessKeysCommand(userName, fetchKeys.AccessKey, fetchKeys.SecretKey, deptCode, roleCode));

        _logger.LogError(string.Join(Environment.NewLine, fetchKeysResult.Errors));
      }

      if (!string.IsNullOrEmpty(ak) && !string.IsNullOrEmpty(sk))
      {
        var userKeys = new AccessKeysCacheItem(ak, sk);

        var serializedKeys = JsonSerializer.Serialize(userKeys);
        await _cache.SetStringAsync(cacheKey, serializedKeys, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(1)
        });

        return (userKeys.AccessKey, userKeys.SecretKey);
      }
    }

    _logger.LogWarning("Could not find access keys for user {UserName} in department {DeptCode}. Falling back to admin keys.", userName, deptCode);
    return await GetAdminKeysAsync();
  }

  public Task<(string AccessKey, string SecretKey)> GetAdminKeysAsync()
  {
    return Task.FromResult((
        _configuration.Value.WITLAB_ADMIN_API_ACCESS_KEY,
        _configuration.Value.WITLAB_ADMIN_API_SECRET_KEY
    ));
  }

  private async Task<Result<(string AccessKey, string SecretKey)>> fetchUserKeys(string requiredUserName, string requiredDeptCode, string? requiredRoleCode)
  {
    var logger = _serviceProvider.GetRequiredService<ILogger<WitLabProxyService>>();
    var httpClientFactory = _serviceProvider.GetRequiredService<IHttpClientFactory>();
    var witLabConfiguration = _serviceProvider.GetRequiredService<IOptionsSnapshot<WitLabConfiguration>>();

    var proxyService = new WitLabProxyService(
        logger,
        _mediator,
        httpClientFactory,
        this,
        witLabConfiguration,
        null // Assuming no current user for this operation
    );

    var syncService = new WitLabSyncService(proxyService);
    var fetchKeysResult = await syncService.GetUserAccessKeysAsync(requiredUserName, requiredDeptCode, requiredRoleCode);
    if (!fetchKeysResult.IsSuccess)
    {
      _logger.LogError(string.Join(Environment.NewLine, fetchKeysResult.Errors));
      return Result.Error(new ErrorList(fetchKeysResult.Errors));
    }

    return fetchKeysResult;
  }
}
