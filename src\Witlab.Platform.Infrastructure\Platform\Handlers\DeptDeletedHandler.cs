﻿using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 部门分配事件处理器
/// </summary>
public class DeptDeletedHandler : INotificationHandler<DeptDeletedEvent>
{
  private readonly ILogger<DeptDeletedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public DeptDeletedHandler(ILogger<DeptDeletedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(DeptDeletedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理部门删除事件: 部门ID {RoleId}, 部门编码 {RoleCode}", notification.DeptId, notification.DeptCode);
    var syncResult = await _witLabSyncService.SyncDeleteDeptAsync(notification.DeptCode);
    if (syncResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, syncResult.Errors));

    _logger.LogInformation("同步至WitLab Server: 部门编码 {RoleCode}", notification.DeptCode);
  }
}
