﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 用户角色关系表
///</summary>
public class UserDept : EntityBase<Guid>
{
  private UserDept()
  {

  }
  public UserDept(Guid userId, Guid deptId)
  {
    UserId = userId;
    DeptId = deptId;
  }

  /// <summary>
  /// 用户id
  /// </summary>
  public Guid UserId { get; init; }
  public User User { get; set; } = null!;

  /// <summary>
  /// 部门id
  /// </summary>
  public Guid DeptId { get; init; }
  public Dept Dept { get; set; } = null!;
}
