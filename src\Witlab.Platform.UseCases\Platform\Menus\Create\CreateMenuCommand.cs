﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.UseCases.Platform.Menus.Create;

/// <summary>
/// 创建菜单命令
/// </summary>
public record CreateMenuCommand(
    string MenuName,
    MenuType MenuType,
    Guid? ParentId,
    int OrderNum,
    string? Icon,
    string? ActiveIcon,
    string? Router,
    string? Component,
    string? RouterName,
    string? Redirect,
    Dictionary<string, string>? Query,
    string? Remark,
    string? Title,
    bool AffixTab,
    int? AffixTabOrder,
    string? Badge,
    string? BadgeType,
    string? BadgeVariants,
    string? IframeSrc,
    string? Link,
    bool? OpenInNewWindow,
    bool? KeepAlive,
    bool? HideInMenu,
    bool? HideInTab,
    bool? HideInBreadcrumb,
    bool? HideChildrenInMenu,
    string? ActivePath,
    int? MaxNumofOpenTab,
    bool? NoBasicLayout,
    string? AuthCode
) : ICommand<Result<MenuDTO>>;
