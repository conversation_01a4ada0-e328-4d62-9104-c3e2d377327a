﻿using System.Text.Json;
using Ardalis.Result;
using Microsoft.Extensions.Caching.Distributed;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// 用于缓存序列化的刷新令牌DTO
/// </summary>
public class RefreshTokenCacheDto
{
  public Guid Id { get; set; }
  public Guid UserId { get; set; }
  public string Token { get; set; } = string.Empty;
  public DateTime ExpiresAt { get; set; }
  public DateTime CreatedAt { get; set; }
  public bool IsUsed { get; set; }
  public bool IsRevoked { get; set; }

  public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
  public bool IsActive => !IsUsed && !IsRevoked && !IsExpired;

  public static RefreshTokenCacheDto FromModel(RefreshToken token)
  {
    return new RefreshTokenCacheDto
    {
      Id = token.Id,
      UserId = token.UserId,
      Token = token.Token,
      ExpiresAt = token.ExpiresAt,
      CreatedAt = token.CreatedAt,
      IsUsed = token.IsUsed,
      IsRevoked = token.IsRevoked
    };
  }

  public RefreshToken ToModel()
  {
    // 创建新的RefreshToken实体
    var token = new RefreshToken(UserId, Token, ExpiresAt);

    // 如果令牌已被使用或撤销，需要更新状态
    if (IsUsed)
    {
      token.Use();
    }
    if (IsRevoked)
    {
      token.Revoke();
    }

    return token;
  }
}

/// <summary>
/// 基于缓存的刷新令牌服务实现
/// </summary>
public class RefreshTokenService : IRefreshTokenService
{
  private readonly IDistributedCache _cache;
  private readonly JwtSettings _jwtSettings;
  private const string CACHE_KEY_PREFIX = "refresh_token_";
  private const string USER_TOKENS_KEY_PREFIX = "user_tokens_";

  public RefreshTokenService(IDistributedCache cache, IOptions<JwtSettings> jwtSettings)
  {
    _cache = cache;
    _jwtSettings = jwtSettings.Value;
  }

  /// <inheritdoc />
  public async Task<Result<RefreshToken>> CreateRefreshTokenAsync(Guid userId, string refreshToken, DateTime expiresAt)
  {
    var token = new RefreshToken(userId, refreshToken, expiresAt);
    var cacheDto = RefreshTokenCacheDto.FromModel(token);

    // 计算缓存过期时间
    var timeToLive = expiresAt - DateTime.UtcNow;
    if (timeToLive <= TimeSpan.Zero)
    {
      throw new ArgumentException("Token expiration time must be in the future", nameof(expiresAt));
    }

    // 存储到缓存
    var cacheKey = $"{CACHE_KEY_PREFIX}{refreshToken}";
    var jsonData = JsonSerializer.Serialize(cacheDto);

    await _cache.SetStringAsync(cacheKey, jsonData, new DistributedCacheEntryOptions
    {
      AbsoluteExpirationRelativeToNow = timeToLive
    });

    // 同时维护用户令牌列表（用于撤销所有用户令牌）
    await AddTokenToUserListAsync(userId, refreshToken, timeToLive);

    return token;
  }

  /// <inheritdoc />
  public async Task<Result<RefreshToken?>> GetRefreshTokenAsync(string token)
  {
    try
    {
      var cacheKey = $"{CACHE_KEY_PREFIX}{token}";
      var jsonData = await _cache.GetStringAsync(cacheKey);

      if (string.IsNullOrEmpty(jsonData))
      {
        return Result<RefreshToken?>.Success(null); // 明确返回一个 Result 包装的 null
      }

      var cacheDto = JsonSerializer.Deserialize<RefreshTokenCacheDto>(jsonData);
      return Result<RefreshToken?>.Success(cacheDto?.ToModel());
    }
    catch
    {
      return Result<RefreshToken?>.Error("An error occurred while retrieving the token.");
    }
  }

  /// <inheritdoc />
  public async Task<Result> UseRefreshTokenAsync(string token)
  {
    try
    {
      var cacheKey = $"{CACHE_KEY_PREFIX}{token}";
      var jsonData = await _cache.GetStringAsync(cacheKey);

      if (string.IsNullOrEmpty(jsonData))
        return Result.Error("Token not found.");

      var cacheDto = JsonSerializer.Deserialize<RefreshTokenCacheDto>(jsonData);
      if (cacheDto == null || !cacheDto.IsActive)
        return Result.Error("Token is not active.");

      // 标记为已使用
      cacheDto.IsUsed = true;

      // 更新缓存
      var updatedJsonData = JsonSerializer.Serialize(cacheDto);
      var timeToLive = cacheDto.ExpiresAt - DateTime.UtcNow;

      if (timeToLive > TimeSpan.Zero)
      {
        await _cache.SetStringAsync(cacheKey, updatedJsonData, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = timeToLive
        });
      }

      return Result.Success();
    }
    catch (Exception ex)
    {
      return Result.Error($"An error occurred while using the token: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> RevokeRefreshTokenAsync(string token)
  {
    try
    {
      var cacheKey = $"{CACHE_KEY_PREFIX}{token}";
      var jsonData = await _cache.GetStringAsync(cacheKey);

      if (string.IsNullOrEmpty(jsonData))
        return Result.Error("Token not found.");

      var cacheDto = JsonSerializer.Deserialize<RefreshTokenCacheDto>(jsonData);
      if (cacheDto == null)
        return Result.Error("Invalid token data.");

      // 标记为已撤销
      cacheDto.IsRevoked = true;

      // 更新缓存
      var updatedJsonData = JsonSerializer.Serialize(cacheDto);
      var timeToLive = cacheDto.ExpiresAt - DateTime.UtcNow;

      if (timeToLive > TimeSpan.Zero)
      {
        await _cache.SetStringAsync(cacheKey, updatedJsonData, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = timeToLive
        });
      }
      else
      {
        // 如果已过期，直接删除
        await _cache.RemoveAsync(cacheKey);
      }

      // 从用户令牌列表中移除
      await RemoveTokenFromUserListAsync(cacheDto.UserId, token);

      return Result.Success();
    }
    catch (Exception ex)
    {
      return Result.Error($"An error occurred while revoking the token: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> RevokeAllUserRefreshTokensAsync(Guid userId)
  {
    try
    {
      var userTokensKey = $"{USER_TOKENS_KEY_PREFIX}{userId}";
      var tokensJson = await _cache.GetStringAsync(userTokensKey);

      if (string.IsNullOrEmpty(tokensJson))
        return Result.Error("No tokens found for the user.");

      var tokenList = JsonSerializer.Deserialize<List<string>>(tokensJson);
      if (tokenList == null || !tokenList.Any())
        return Result.Error("No valid tokens found for the user.");

      var revokedCount = 0;
      foreach (var token in tokenList)
      {
        var result = await RevokeRefreshTokenAsync(token);
        if (result.IsSuccess)
          revokedCount++;
      }

      // 清空用户令牌列表
      await _cache.RemoveAsync(userTokensKey);

      return revokedCount > 0
          ? Result.Success()
          : Result.Error("No tokens were successfully revoked.");
    }
    catch (Exception ex)
    {
      return Result.Error($"An error occurred while revoking tokens: {ex.Message}");
    }
  }

  /// <summary>
  /// 将令牌添加到用户令牌列表
  /// </summary>
  private async Task AddTokenToUserListAsync(Guid userId, string token, TimeSpan timeToLive)
  {
    try
    {
      var userTokensKey = $"{USER_TOKENS_KEY_PREFIX}{userId}";
      var tokensJson = await _cache.GetStringAsync(userTokensKey);

      var tokenList = string.IsNullOrEmpty(tokensJson)
          ? new List<string>()
          : JsonSerializer.Deserialize<List<string>>(tokensJson) ?? new List<string>();

      if (!tokenList.Contains(token))
      {
        tokenList.Add(token);
        var updatedJson = JsonSerializer.Serialize(tokenList);

        await _cache.SetStringAsync(userTokensKey, updatedJson, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = timeToLive.Add(TimeSpan.FromMinutes(5)) // 稍微延长一点时间
        });
      }
    }
    catch
    {
      // 忽略错误，不影响主要功能
    }
  }

  /// <summary>
  /// 从用户令牌列表中移除令牌
  /// </summary>
  private async Task RemoveTokenFromUserListAsync(Guid userId, string token)
  {
    try
    {
      var userTokensKey = $"{USER_TOKENS_KEY_PREFIX}{userId}";
      var tokensJson = await _cache.GetStringAsync(userTokensKey);

      if (string.IsNullOrEmpty(tokensJson))
        return;

      var tokenList = JsonSerializer.Deserialize<List<string>>(tokensJson);
      if (tokenList != null && tokenList.Remove(token))
      {
        if (tokenList.Any())
        {
          var updatedJson = JsonSerializer.Serialize(tokenList);
          await _cache.SetStringAsync(userTokensKey, updatedJson, new DistributedCacheEntryOptions
          {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(_jwtSettings.RefreshTokenExpirationDays)
          });
        }
        else
        {
          await _cache.RemoveAsync(userTokensKey);
        }
      }
    }
    catch
    {
      // 忽略错误，不影响主要功能
    }
  }
}
