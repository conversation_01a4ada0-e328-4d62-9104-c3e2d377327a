﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.Delete;

/// <summary>
/// 删除角色命令处理器
/// </summary>
public class DeleteRoleHandler : ICommandHandler<DeleteRoleCommand, Result>
{
  private readonly IRoleService _roleService;

  public DeleteRoleHandler(IRoleService roleService)
  {
    _roleService = roleService;
  }

  public async Task<Result> Handle(DeleteRoleCommand request, CancellationToken cancellationToken)
  {
    var result = await _roleService.GetRoleAsync(request.RoleId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }
    var role = result.Value;

    var deleteResult = await _roleService.DeleteRoleAsync(request.RoleId);
    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(deleteResult.Errors));
    }

    return Result.Success();
  }
}
