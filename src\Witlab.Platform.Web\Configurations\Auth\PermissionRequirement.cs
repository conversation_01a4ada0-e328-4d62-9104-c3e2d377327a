﻿using Microsoft.AspNetCore.Authorization;

namespace Witlab.Platform.Web.Configurations.Auth;

/// <summary>
/// 权限验证要求
/// </summary>
public class PermissionRequirement : IAuthorizationRequirement
{
  /// <summary>
  /// 权限编码
  /// </summary>
  public string Permission { get; }

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="permission">权限编码</param>
  public PermissionRequirement(string permission)
  {
    Permission = permission;
  }
}
