﻿namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// JWT配置
/// </summary>
public class JwtSettings
{
  /// <summary>
  /// 密钥
  /// </summary>
  public string Secret { get; set; } = string.Empty;

  /// <summary>
  /// 发行者
  /// </summary>
  public string Issuer { get; set; } = string.Empty;

  /// <summary>
  /// 接收者
  /// </summary>
  public string Audience { get; set; } = string.Empty;

  /// <summary>
  /// 访问令牌有效期（分钟）
  /// </summary>
  public int AccessTokenExpirationMinutes { get; set; } = 30;

  /// <summary>
  /// 刷新令牌有效期（天）
  /// </summary>
  public int RefreshTokenExpirationDays { get; set; } = 7;
}
