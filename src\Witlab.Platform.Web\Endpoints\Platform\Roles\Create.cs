﻿using Witlab.Platform.UseCases.Platform.Roles.Create;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 创建角色
/// </summary>
public class Create(IMediator _mediator) : Endpoint<CreateRoleRequest, CreateRoleResponse>
{
  public override void Configure()
  {
    Post(CreateRoleRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "创建新角色";
      s.Description = "创建一个新角色，需要提供角色名称和角色编码";
      s.ExampleRequest = new CreateRoleRequest { RoleName = "管理员", RoleCode = "admin" };
    });
  }

  public override async Task HandleAsync(CreateRoleRequest request, CancellationToken cancellationToken)
  {
    var command = new CreateRoleCommand(
        request.RoleName!,
        request.RoleCode!,
        request.Permissions,
        request.Remark,
        request.Status == 1 ? true : false
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      var dto = result.Value;
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync();
      return;
    }

    await SendErrorsAsync();
  }
}
