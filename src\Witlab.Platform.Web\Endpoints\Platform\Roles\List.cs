﻿using Witlab.Platform.UseCases.Platform.Roles.List;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 列出所有角色
/// </summary>
public class List(IMediator _mediator) : Endpoint<ListRolesRequest, List<RoleRecord>>
{
  public override void Configure()
  {
    Get(ListRolesRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "列出所有角色";
      s.Description = "获取系统中的所有角色，支持分页";
    });
  }

  public override async Task HandleAsync(ListRolesRequest request, CancellationToken cancellationToken)
  {
    var query = new ListRolesQuery(request.Skip, request.Take);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var roles = result.Value.Select(dto => new RoleRecord()
      {
        Id = dto.Id.ToString(),
        RoleName = dto.RoleName,
        RoleCode = dto.RoleCode,
        Remark = dto.Remark,
        Status = dto.State ? 1 : 0,
        Permissions = dto.Permissions.ToList(),
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      }).ToList();

      Response = roles;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
