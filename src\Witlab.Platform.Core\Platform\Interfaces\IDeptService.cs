﻿namespace Witlab.Platform.Core.Platform.Interfaces;

/// <summary>
/// 部门领域服务接口
/// </summary>
public interface IDeptService
{
  /// <summary>
  /// 创建部门
  /// </summary>
  /// <param name="deptName">部门名称</param>
  /// <param name="deptCode">部门编码</param>
  /// <param name="parentId">父部门ID，如果为null则为顶级部门</param>
  /// <param name="leader">负责人</param>
  /// <param name="orderNum">排序号</param>
  /// <param name="remark">备注</param>
  /// <returns>创建的部门</returns>
  Task<Result<Dept>> CreateDeptAsync(string deptName, string deptCode, Guid? parentId = null, string? leader = null, int orderNum = 0, string? remark = null, bool status = true);

  /// <summary>
  /// 更新部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <param name="deptName">部门名称</param>
  /// <param name="deptCode">部门编码</param>
  /// <param name="parentId">父部门ID</param>
  /// <param name="leader">负责人</param>
  /// <param name="orderNum">排序号</param>
  /// <param name="remark">备注</param>
  /// <returns>更新结果</returns>
  Task<Result<Dept>> UpdateDeptAsync(Guid deptId, string deptName, string deptCode, Guid? parentId, string? leader = null, int orderNum = 0, string? remark = null, bool status = true);

  /// <summary>
  /// 删除部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <returns>删除结果</returns>
  Task<Result> DeleteDeptAsync(Guid deptId);

  /// <summary>
  /// 获取部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <returns>部门</returns>
  Task<Result<Dept>> GetDeptAsync(Guid deptId);

  /// <summary>
  /// 根据部门编码获取部门
  /// </summary>
  /// <param name="deptCode">部门编码</param>
  /// <returns>部门</returns>
  Task<Result<Dept>> GetDeptByCodeAsync(string deptCode);

  /// <summary>
  /// 获取部门树
  /// </summary>
  /// <param name="parentId">父部门ID，如果为null则获取所有部门</param>
  /// <returns>部门树</returns>
  Task<Result<List<Dept>>> GetDeptTreeAsync(Guid? parentId = null);

  /// <summary>
  /// 获取子部门
  /// </summary>
  /// <param name="parentId">父部门ID</param>
  /// <returns>子部门列表</returns>
  Task<Result<List<Dept>>> GetChildDeptAsync(Guid parentId);

  /// <summary>
  /// 激活部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <returns>激活结果</returns>
  Task<Result> ActivateDeptAsync(Guid deptId);

  /// <summary>
  /// 禁用部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <returns>禁用结果</returns>
  Task<Result> DeactivateDeptAsync(Guid deptId);

  /// <summary>
  /// 分配用户到部门
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <param name="userIds">用户ID列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignUsersToDeptAsync(Guid deptId, List<Guid> userIds);

  /// <summary>
  /// 获取部门用户
  /// </summary>
  /// <param name="deptId">部门ID</param>
  /// <returns>用户列表</returns>
  Task<Result<List<User>>> GetDeptUsersAsync(Guid deptId);
}
