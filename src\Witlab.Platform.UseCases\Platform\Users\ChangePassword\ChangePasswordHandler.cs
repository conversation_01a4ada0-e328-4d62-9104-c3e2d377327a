﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.ChangePassword;

/// <summary>
/// 修改用户密码命令处理器
/// </summary>
public class ChangePasswordHandler : ICommandHandler<ChangePasswordCommand, Result>
{
  private readonly IUserService _userService;

  public ChangePasswordHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
  {
    return await _userService.ChangePasswordAsync(request.UserId, request.OldPassword, request.NewPassword);
  }
}
