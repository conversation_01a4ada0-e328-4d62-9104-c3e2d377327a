﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>WitLab.Platform.SharedKernel</AssemblyName>
    <PackageId>WitLab.Platform.SharedKernel</PackageId>
    <Title>WitLab.Platform.SharedKernel</Title>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Authors><PERSON> (@ardalis)</Authors>
    <Company>Ardalis.com</Company>
    <Description>An example shared kernel project/package for use with Ardalis.CleanArchitecture template. You should probably replace this with your own package!</Description>
    <Summary>An example shared kernel project/package for use with <PERSON>rdalis.CleanArchitecture template</Summary>
    <PackageProjectUrl>https://github.com/ardalis/Ardalis.SharedKernel</PackageProjectUrl>
    <RepositoryUrl>https://github.com/ardalis/Ardalis.SharedKernel</RepositoryUrl>
    <PackageTags>DDD;Shared Kernel;SharedKernel;Domain-Driven Design;Repository;Specification;ValueObject;Value Object;Ardalis;Clean;Clean Architecture;Clean Architecture Template</PackageTags>
    <PackageIcon>icon.png</PackageIcon>
    <Version>2.1.1</Version>
    <PackageReleaseNotes>
      * Updated packages
    </PackageReleaseNotes>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <AllowedOutputExtensionsInPackageBuildOutputFolder>$(AllowedOutputExtensionsInPackageBuildOutputFolder);.pdb</AllowedOutputExtensionsInPackageBuildOutputFolder>
    <DocumentationFile>bin\$(Configuration)\WitLab.Platform.SharedKernel.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="Ardalis.Specification" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <None Include="icon.png" Pack="true" Visible="false" PackagePath="" />
  </ItemGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;1573;1712</NoWarn>
  </PropertyGroup>


</Project>
