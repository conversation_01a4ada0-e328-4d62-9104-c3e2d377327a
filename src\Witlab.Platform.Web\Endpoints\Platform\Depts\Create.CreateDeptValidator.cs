﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 创建部门请求验证器
/// </summary>
public class CreateDeptValidator : Validator<CreateDeptRequest>
{
  public CreateDeptValidator()
  {
    RuleFor(x => x.DeptName)
        .NotEmpty().WithMessage("部门名称不能为空")
        .MinimumLength(2).WithMessage("部门名称长度不能少于2个字符")
        .MaximumLength(50).WithMessage("部门名称长度不能超过50个字符");

    RuleFor(x => x.DeptCode)
        .NotEmpty().WithMessage("部门编码不能为空")
        .MinimumLength(2).WithMessage("部门编码长度不能少于2个字符")
        .MaximumLength(50).WithMessage("部门编码长度不能超过50个字符");

    RuleFor(x => x.Leader)
        .MaximumLength(50).WithMessage("负责人长度不能超过50个字符")
        .When(x => !string.IsNullOrEmpty(x.Leader));

    RuleFor(x => x.Remark)
        .MaximumLength(500).WithMessage("备注长度不能超过500个字符")
        .When(x => !string.IsNullOrEmpty(x.Remark));
  }
}
