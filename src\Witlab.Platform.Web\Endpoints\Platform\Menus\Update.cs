﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Menus.Update;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 更新菜单
/// </summary>
[RequirePermission("menu:update")]
public class Update(IMediator _mediator) : Endpoint<UpdateMenuRequest, UpdateMenuResponse>
{
  public override void Configure()
  {
    Put(UpdateMenuRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "更新菜单";
      s.Description = "更新菜单信息";
    });
  }

  public override async Task HandleAsync(UpdateMenuRequest request, CancellationToken cancellationToken)
  {
    var menuType = MenuType.FromName(request.Type, true);

    var command = new UpdateMenuCommand(
        request.Id,
        request.Name,
        menuType,
        request.AuthCode,
        request.Pid,
        request.Meta.Order,
        request.Meta.Icon,
        request.Meta.ActiveIcon,
        request.Path,
        request.Component,
        request.Name,
        request.Redirect,
        request.Meta.Query,
        null, // remark
        request.Meta.Title,
        request.Meta.AffixTab,
        request.Meta.AffixTabOrder,
        request.Meta.Badge,
        request.Meta.BadgeType,
        request.Meta.BadgeVariants,
        request.Meta.IframeSrc,
        request.Meta.Link,
        request.Meta.OpenInNewWindow,
        request.Meta.KeepAlive,
        request.Meta.HideInMenu,
        request.Meta.HideInTab,
        request.Meta.HideInBreadcrumb,
        request.Meta.HideChildrenInMenu,
        request.Meta.ActivePath,
        request.Meta.MaxNumofOpenTab,
        request.Meta.NoBasicLayout
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      //var dto = result.Value;
      //Response = new UpdateMenuResponse(
      //    dto.DeptId,
      //    dto.MenuName,
      //    dto.ParentId,
      //    dto.MenuTypeValue,
      //    dto.MenuTypeName,
      //    dto.OrderNum,
      //    dto.StateValue,
      //    dto.StateName,
      //    dto.MenuIcon,
      //    dto.ActiveIcon,
      //    dto.Router,
      //    dto.RouterName,
      //    dto.Action,
      //    dto.Query,
      //    dto.Remark,
      //    dto.Title,
      //    dto.AffixTab,
      //    dto.AffixTabOrder,
      //    dto.Badge,
      //    dto.BadgeType,
      //    dto.BadgeVariants,
      //    dto.IframeSrc,
      //    dto.Link,
      //    dto.OpenInNewWindow,
      //    dto.KeepAlive,
      //    dto.HideInMenu,
      //    dto.HideInTab,
      //    dto.HideInBreadcrumb,
      //    dto.HideChildrenInMenu,
      //    dto.ActivePath,
      //    dto.MaxNumofOpenTab,
      //    dto.NoBasicLayout
      //);
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
