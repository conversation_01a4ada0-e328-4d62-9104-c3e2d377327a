﻿namespace Witlab.Platform.Core.Platform;
public class RoleDataScope : SmartEnum<RoleDataScope>
{
  public static readonly RoleDataScope All = new RoleDataScope("所有数据", 0);
  public static readonly RoleDataScope Custom = new RoleDataScope("自定义数据", 1);
  public static readonly RoleDataScope Own = new RoleDataScope("仅本人数据", 2);
  public static readonly RoleDataScope Department = new RoleDataScope("本部门数据", 3);
  protected RoleDataScope(string name, int value) : base(name, value)
  {
  }
}

