﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据角色编码分配角色给用户请求验证器
/// </summary>
public class AssignRolesToUserByCodesValidator : Validator<AssignRolesToUserByCodesRequest>
{
  public AssignRolesToUserByCodesValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.RoleCodes)
        .NotNull().WithMessage("角色编码列表不能为空");

    RuleForEach(x => x.RoleCodes)
        .NotEmpty().WithMessage("角色编码不能为空")
        .MaximumLength(50).WithMessage("角色编码长度不能超过50个字符");
  }
}
