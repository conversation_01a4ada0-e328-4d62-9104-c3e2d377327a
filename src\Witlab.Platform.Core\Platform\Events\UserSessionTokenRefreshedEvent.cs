﻿namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 用户会话Token刷新事件
/// </summary>
public class UserSessionTokenRefreshedEvent : DomainEventBase
{
    /// <summary>
    /// 会话ID
    /// </summary>
    public Guid SessionId { get; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; }

    /// <summary>
    /// 新的Token ID
    /// </summary>
    public string NewTokenId { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="userName">用户名</param>
    /// <param name="newTokenId">新的Token ID</param>
    public UserSessionTokenRefreshedEvent(Guid sessionId, Guid userId, string userName, string newTokenId)
    {
        SessionId = sessionId;
        UserId = userId;
        UserName = userName;
        NewTokenId = newTokenId;
    }
}
