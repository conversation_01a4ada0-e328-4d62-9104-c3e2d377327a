﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 菜单类型
/// </summary>
public class MenuType : SmartEnum<MenuType>
{
  /// <summary>
  /// 目录
  /// </summary>
  public static readonly MenuType Catalog = new(nameof(Catalog), 0);

  /// <summary>
  /// 菜单
  /// </summary>
  public static readonly MenuType Menu = new(nameof(Menu), 1);

  /// <summary>
  /// 组件/按钮
  /// </summary>
  public static readonly MenuType Action = new(nameof(Action), 2);

  /// <summary>
  /// 嵌入式页面
  /// </summary>
  public static readonly MenuType Embedded = new(nameof(Embedded), 3);

  /// <summary>
  /// 外部链接
  /// </summary>
  public static readonly MenuType Link = new(nameof(Link), 4);

  protected MenuType(string name, int value) : base(name, value) { }
}
