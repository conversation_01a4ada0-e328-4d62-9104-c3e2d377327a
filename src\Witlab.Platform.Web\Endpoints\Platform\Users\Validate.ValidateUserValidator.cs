﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 验证用户密码请求验证器
/// </summary>
public class ValidateUserValidator : Validator<ValidateUserRequest>
{
  public ValidateUserValidator()
  {
    RuleFor(x => x.UserName)
        .NotEmpty().WithMessage("用户名不能为空")
        .MinimumLength(3).WithMessage("用户名长度不能少于3个字符")
        .MaximumLength(50).WithMessage("用户名长度不能超过50个字符");

    RuleFor(x => x.Password)
        .NotEmpty().WithMessage("密码不能为空")
        .MinimumLength(6).WithMessage("密码长度不能少于6个字符")
        .MaximumLength(100).WithMessage("密码长度不能超过100个字符");
  }
}
