﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Permissions.Create;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 创建权限
/// </summary>
public class Create(IMediator _mediator) : Endpoint<CreatePermissionRequest, CreatePermissionResponse>
{
  public override void Configure()
  {
    Post(CreatePermissionRequest.Route);
    Description(x => x.AutoTagOverride("Permission"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "创建新权限";
      s.Description = "创建一个新权限，需要提供权限编码、名称和类型";
      s.ExampleRequest = new CreatePermissionRequest { Code = "user:create", Name = "创建用户", TypeValue = 0 };
    });
  }

  public override async Task HandleAsync(CreatePermissionRequest request, CancellationToken cancellationToken)
  {
    var permissionType = PermissionType.FromValue(request.TypeValue);

    var command = new CreatePermissionCommand(
        request.Code!,
        request.Name!,
        permissionType,
        request.Description,
        request.MenuId
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new CreatePermissionResponse(
          dto.Id,
          dto.Code,
          dto.Name,
          dto.Description,
          dto.TypeValue,
          dto.TypeName
      );
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
