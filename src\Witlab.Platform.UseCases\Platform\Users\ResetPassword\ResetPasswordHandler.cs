﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.ResetPassword;

/// <summary>
/// 重置用户密码命令处理器
/// </summary>
public class ResetPasswordHandler : ICommandHandler<ResetPasswordCommand, Result>
{
  private readonly IUserService _userService;

  public ResetPasswordHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
  {
    return await _userService.ResetPasswordAsync(request.UserId, request.NewPassword);
  }
}
