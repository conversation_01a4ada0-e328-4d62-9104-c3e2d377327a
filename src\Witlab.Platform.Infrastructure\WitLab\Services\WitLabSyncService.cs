﻿using System.Text;
using System.Text.Json;
using Ardalis.Result;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.WitLab.Services;

public class WitLabSyncService : IWitLabSyncService
{
  private readonly IWitLabProxyService _proxyService;

  public WitLabSyncService(IWitLabProxyService proxyService)
  {
    _proxyService = proxyService;
  }

  public async Task<Result> SyncAddDeptAsync(string deptCode, string shareDB = "External", string? deptIdentificationCode = null, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("LABS.ADD_LAB", [deptCode, shareDB, deptIdentificationCode], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public Task<Result> SyncUpdateDeptAsync(string deptCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default)
  {
    throw new NotImplementedException();
  }

  public async Task<Result> SyncDeleteDeptAsync(string deptCode, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("LABS.DEL_LAB", [deptCode], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncAddRoleAsync(string roleCode, string roleName, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("RoleManagement.AddRole", [roleCode, roleName], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public Task<Result> SyncUpdateRoleAsync(string roleCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default)
  {
    throw new NotImplementedException();
  }

  public async Task<Result> SyncDeleteRoleAsync(string roleCode, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("RoleManagement.DeleteRole", [roleCode], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncAddUserInfoAsync(string userName, string fullName, string? email, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("UserManagement.SyncFromWitlab.SyncAddUserInfo", [userName, fullName, email], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncDeleteUserInfoAsync(string userName, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("UserManagement.SyncFromWitlab.SyncDeleteUserInfo", [userName], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncChangeUserPasswordAsync(string userName, string password, CancellationToken cancellationToken = default)
  {
    //var responseResult = await PostServerScriptRequest("UserManagement.ChangePassword", [MaskString(userName), "BPW", MaskString(password), true], cancellationToken);
    var responseResult = await PostServerScriptRequest("Security_Module.ChangePassword", [userName, "BPW", password, false], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncUserDeptsAsync(string userName, List<string> deptCodes, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("UserManagement.SyncFromWitlab.SyncUserDept", [userName, deptCodes], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result> SyncUserRolesAsync(string userName, List<string> roleCodes, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("UserManagement.addRoles", [userName, roleCodes], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }
    return Result.Success();
  }

  public async Task<Result<(string AccessKey, string SecretKey)>> GetUserAccessKeysAsync(string userName, string deptCode, string? roleCode = null, CancellationToken cancellationToken = default)
  {
    var responseResult = await PostServerScriptRequest("Common.GetRestApiKeys", [userName, deptCode, roleCode], cancellationToken);

    if (!responseResult.IsSuccess)
    {
      return Result.Error(new ErrorList(responseResult.Errors));
    }

    var response = responseResult.Value;

    if (!response.Success)
    {
      return Result.Error(response.Message);
    }

    var accessKeysResult = response.Result?.ToString();

    Guard.Against.Null(accessKeysResult, nameof(accessKeysResult), "Access keys cannot be null.");

    var accessKeys = JsonSerializer.Deserialize<List<string?>>(accessKeysResult);

    var accessKey = accessKeys?.FirstOrDefault();
    Guard.Against.NullOrEmpty(accessKey, nameof(accessKey), "Access keys cannot be null.");
    var secretKey = accessKeys?.LastOrDefault();
    Guard.Against.NullOrEmpty(secretKey, nameof(secretKey), "Access keys cannot be null.");

    return Result.Success((accessKey, secretKey));
  }

  private async Task<Result<WitLabApiResponse>> PostServerScriptRequest(string scriptName, List<object?>? parameters, CancellationToken cancellationToken = default)
  {
    var request = new WitLabApiRequest()
    {
      Method = "POST",
      ContentType = "application/json"
    };

    var json = JsonSerializer.Serialize(new
    {
      Type = "ServerScript",
      ScriptName = scriptName,
      Params = parameters
    });

    request.Body = new MemoryStream(Encoding.UTF8.GetBytes(json));

    try
    {
      var response = await _proxyService.ForwardAsync(request, "v1/WitLab/Proxy", cancellationToken);

      return response;
    }
    catch (Exception ex)
    {
      return Result.Error(ex.Message);
    }
  }
}
