﻿namespace Witlab.Platform.Core.Platform.Interfaces;

/// <summary>
/// 用户领域服务接口
/// </summary>
public interface IUserService
{
  /// <summary>
  /// 创建用户
  /// </summary>
  /// <param name="userName">用户名</param>
  /// <param name="password">密码</param>
  /// <param name="fullName">姓名</param>
  /// <param name="email">邮箱</param>
  /// <returns>创建的用户</returns>
  Task<Result<User>> CreateUserAsync(string userName, string password, string fullName, List<string>? depts, List<string>? roles, string? email = null, long? phone = null, string? address = null, string? icon = null, int? sexValue = 2, int? stateVaule = 1, string? remark = null);

  /// <summary>
  /// 更新用户信息
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="fullName">姓名</param>
  /// <param name="email">邮箱</param>
  /// <param name="phone">电话</param>
  /// <param name="address">地址</param>
  /// <param name="sex">性别</param>
  /// <param name="remark">备注</param>
  /// <returns>更新结果</returns>
  Task<Result<User>> UpdateUserAsync(Guid userId, string fullName, List<string>? depts, List<string>? roles, string? email = null, long? phone = null, string? address = null, string? icon = null, int? sexValue = 2, int? stateVaule = 1, string? remark = null);

  /// <summary>
  /// 删除用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>删除结果</returns>
  Task<Result> DeleteUserAsync(Guid userId);

  /// <summary>
  /// 获取用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>用户</returns>
  Task<Result<User>> GetUserAsync(Guid userId);

  /// <summary>
  /// 根据用户名获取用户
  /// </summary>
  /// <param name="userName">用户名</param>
  /// <returns>用户</returns>
  Task<Result<User>> GetUserByNameAsync(string userName);

  /// <summary>
  /// 验证用户密码
  /// </summary>
  /// <param name="userName">用户名</param>
  /// <param name="password">密码</param>
  /// <returns>验证结果</returns>
  Task<Result<User>> ValidateUserAsync(string userName, string password);

  /// <summary>
  /// 修改用户密码
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="oldPassword">旧密码</param>
  /// <param name="newPassword">新密码</param>
  /// <returns>修改结果</returns>
  Task<Result> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword);

  /// <summary>
  /// 重置用户密码
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="newPassword">新密码</param>
  /// <returns>重置结果</returns>
  Task<Result> ResetPasswordAsync(Guid userId, string newPassword);

  /// <summary>
  /// 分配角色给用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="roleIds">角色ID列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignRolesToUserAsync(Guid userId, List<Guid> roleIds);

  /// <summary>
  /// 分配角色给用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="roleCodes">角色编码列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignRolesToUserAsync(Guid userId, List<string> roleCodes);

  /// <summary>
  /// 分配部门给用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="deptIds">部门ID列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignDeptsToUserAsync(Guid userId, List<Guid> deptIds);

  /// <summary>
  /// 分配部门给用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="deptCodes">部门编码列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignDeptsToUserAsync(Guid userId, List<string> deptCodes);

  /// <summary>
  /// 获取用户的角色
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>角色列表</returns>
  Task<Result<List<Role>>> GetUserRolesAsync(Guid userId);

  /// <summary>
  /// 获取用户的部门
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>部门列表</returns>
  Task<Result<List<Dept>>> GetUserDeptsAsync(Guid userId);

  /// <summary>
  /// 激活用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>激活结果</returns>
  Task<Result> ActivateUserAsync(Guid userId);

  /// <summary>
  /// 禁用用户
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>禁用结果</returns>
  Task<Result> DeactivateUserAsync(Guid userId);
}
