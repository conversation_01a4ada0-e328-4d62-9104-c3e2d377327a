﻿namespace Witlab.Platform.Core.Platform.Interfaces;

/// <summary>
/// 角色领域服务接口
/// </summary>
public interface IRoleService
{
  /// <summary>
  /// 创建角色
  /// </summary>
  /// <param name="roleName">角色名称</param>
  /// <param name="roleCode">角色编码</param>
  /// <param name="remark">备注</param>
  /// <returns>创建的角色</returns>
  Task<Result<Role>> CreateRoleAsync(string roleName, string roleCode, List<string> permissions, string? remark, bool status);

  /// <summary>
  /// 更新角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <param name="roleName">角色名称</param>
  /// <param name="roleCode">角色编码</param>
  /// <param name="remark">备注</param>
  /// <returns>更新结果</returns>
  Task<Result<Role>> UpdateRoleAsync(Guid roleId, string roleName, string roleCode, List<string> permissions, string? remark, bool status);

  /// <summary>
  /// 删除角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>删除结果</returns>
  Task<Result> DeleteRoleAsync(Guid roleId);

  /// <summary>
  /// 获取角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>角色</returns>
  Task<Result<Role>> GetRoleAsync(Guid roleId);

  /// <summary>
  /// 根据角色编码获取角色
  /// </summary>
  /// <param name="roleCode">角色编码</param>
  /// <returns>角色</returns>
  Task<Result<Role>> GetRoleByCodeAsync(string roleCode);

  /// <summary>
  /// 分配权限给角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <param name="permissionIds">权限ID列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignPermissionsToRoleAsync(Guid roleId, List<Guid> permissionIds);

  /// <summary>
  /// 分配菜单给角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <param name="menuIds">菜单ID列表</param>
  /// <returns>分配结果</returns>
  Task<Result> AssignMenusToRoleAsync(Guid roleId, List<Guid> menuIds);

  /// <summary>
  /// 获取角色的权限
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>权限列表</returns>
  Task<Result<List<Permission>>> GetRolePermissionsAsync(Guid roleId);

  /// <summary>
  /// 获取角色的菜单
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>菜单列表</returns>
  Task<Result<List<Menu>>> GetRoleMenusAsync(Guid roleId);

  /// <summary>
  /// 激活角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>激活结果</returns>
  Task<Result> ActivateRoleAsync(Guid roleId);

  /// <summary>
  /// 禁用角色
  /// </summary>
  /// <param name="roleId">角色ID</param>
  /// <returns>禁用结果</returns>
  Task<Result> DeactivateRoleAsync(Guid roleId);
}
