﻿using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 部门分配事件处理器
/// </summary>
public class DeptCreatedHandler : INotificationHandler<DeptCreatedEvent>
{
  private readonly ILogger<DeptCreatedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public DeptCreatedHandler(ILogger<DeptCreatedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(DeptCreatedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理部门创建事件: 部门ID {RoleId}, 部门编码 {RoleCode}, 部门名称 {RoleName}", notification.DeptId, notification.DeptCode, notification.DeptName);
    var syncResult = await _witLabSyncService.SyncAddDeptAsync(notification.DeptCode);
    if (syncResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, syncResult.Errors));

    _logger.LogInformation("同步至WitLab Server: 部门编码 {RoleCode}, 部门名称：{ UserName }", notification.DeptCode, notification.DeptName);
  }
}
