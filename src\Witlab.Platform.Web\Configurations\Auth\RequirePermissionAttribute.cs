﻿using Microsoft.AspNetCore.Authorization;

namespace Witlab.Platform.Web.Configurations.Auth;

/// <summary>
/// 权限验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class RequirePermissionAttribute : AuthorizeAttribute
{
  public static readonly string POLICY_PREFIX = "WITLAB_POLICY_";

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="permission">权限编码</param>
  public RequirePermissionAttribute(string permission)
      : base(policy: $"Permission:{permission}")
  {
  }
}
