﻿namespace Witlab.Platform.Core.Platform;
public class EncryPassword : ValueObject
{
  public EncryPassword() { }
  public EncryPassword(string password) { Password = password; }

  /// <summary>
  /// 密码
  /// </summary>
  public string Password { get; set; } = string.Empty;

  /// <summary>
  /// 加密盐值
  /// </summary>
  public string Salt { get; set; } = string.Empty;

  protected override IEnumerable<object> GetEqualityComponents()
  {
    yield return Password;
    yield return Salt;
  }
}
