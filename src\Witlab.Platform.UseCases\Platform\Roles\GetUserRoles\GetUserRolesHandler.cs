﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.GetUserRoles;

/// <summary>
/// 获取用户角色查询处理器
/// </summary>
public class GetUserRolesHandler : IQueryHandler<GetUserRolesQuery, Result<List<RoleDTO>>>
{
  private readonly IUserService _userService;

  public GetUserRolesHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result<List<RoleDTO>>> Handle(GetUserRolesQuery request, CancellationToken cancellationToken)
  {
    var result = await _userService.GetUserRolesAsync(request.UserId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var roles = result.Value;
    var roleDtos = roles.Select(MapToDto).ToList();
    return Result.Success(roleDtos);
  }

  private static RoleDTO MapToDto(Role role)
  {
    return new RoleDTO(
        role.Id,
        role.RoleName,
        role.RoleCode,
        role.Remark,
        role.State,
        new List<string>(), // 这里需要获取角色的权限，但UserService.GetUserRolesAsync不返回权限信息
        role.Created.DateTime,
        role.CreatedBy,
        role.LastModified.DateTime,
        role.LastModifiedBy
    );
  }
}
