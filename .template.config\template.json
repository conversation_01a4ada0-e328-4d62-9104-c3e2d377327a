{"$schema": "http://json.schemastore.org/template", "author": "<PERSON> @a<PERSON><PERSON>, <PERSON>", "classifications": ["Web", "ASP.NET", "Clean Architecture"], "tags": {"language": "C#", "type": "project"}, "identity": "Ardalis.CleanArchitecture.Template", "name": "ASP.NET Clean Architecture Solution", "shortName": "clean-arch", "sourceName": "Witlab.Platform", "preferNameDirectory": true, "symbols": {"aspire": {"type": "parameter", "datatype": "bool", "defaultValue": "false", "description": "Include .NET Aspire."}}, "sources": [{"exclude": [".vs/**", ".vscode/**", ".git/**", ".github/**", ".template.config", "sample/**"], "modifiers": [{"condition": "(!aspire)", "exclude": ["src/Witlab.Platform.AspireHost/**", "src/Witlab.Platform.ServiceDefaults/**", "tests/Witlab.Platform.AspireTests/**"]}, {"condition": "(aspire)", "include": ["src/Witlab.Platform.AspireHost/**", "src/Witlab.Platform.ServiceDefaults/**", "tests/Witlab.Platform.AspireTests/**"]}]}]}