﻿using System.Reflection;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Behaviors;
using Witlab.Platform.Infrastructure.Platform.Handlers;
using Witlab.Platform.UseCases.Platform.Users.Create;
using WitLab.Platform.SharedKernel;

namespace Witlab.Platform.Web.Configurations;

public static class MediatrConfigs
{
  public static IServiceCollection AddMediatrConfigs(this IServiceCollection services)
  {
    var mediatRAssemblies = new[]
      {
        Assembly.GetAssembly(typeof(User)), // Core
        Assembly.GetAssembly(typeof(CreateUserCommand)), // UseCases
        Assembly.GetAssembly(typeof(UserCreatedHandler)), // Infrastructure Event Handlers
      };

    services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(mediatRAssemblies!)
                                  .AddOpenBehavior(typeof(CommandUnitOfWorkBehavior<,>)))
            .AddScoped(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>))
            .AddScoped<IDomainEventDispatcher, MediatRDomainEventDispatcher>();

    return services;
  }
}
