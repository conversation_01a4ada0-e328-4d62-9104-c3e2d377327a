﻿using Ardalis.Result;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Sessions;

/// <summary>
/// 强制下线命令
/// </summary>
public record ForceLogoutCommand(
  Guid SessionId,
  string? Reason = null
) : ICommand<Result>;

/// <summary>
/// 强制下线命令处理器
/// </summary>
public class ForceLogoutCommandHandler : ICommandHandler<ForceLogoutCommand, Result>
{
  private readonly IUserSessionService _userSessionService;

  public ForceLogoutCommandHandler(IUserSessionService userSessionService)
  {
    _userSessionService = userSessionService;
  }

  public async Task<Result> Handle(ForceLogoutCommand request, CancellationToken cancellationToken)
  {
    return await _userSessionService.ForceTerminateSessionAsync(request.SessionId, request.Reason);
  }
}

/// <summary>
/// 强制下线用户所有会话命令
/// </summary>
public record ForceLogoutAllUserSessionsCommand(
  Guid UserId,
  string? Reason = null
) : ICommand<Result>;

/// <summary>
/// 强制下线用户所有会话命令处理器
/// </summary>
public class ForceLogoutAllUserSessionsCommandHandler : ICommandHandler<ForceLogoutAllUserSessionsCommand, Result>
{
  private readonly IUserSessionService _userSessionService;

  public ForceLogoutAllUserSessionsCommandHandler(IUserSessionService userSessionService)
  {
    _userSessionService = userSessionService;
  }

  public async Task<Result> Handle(ForceLogoutAllUserSessionsCommand request, CancellationToken cancellationToken)
  {
    return await _userSessionService.ForceTerminateAllUserSessionsAsync(request.UserId, request.Reason);
  }
}
