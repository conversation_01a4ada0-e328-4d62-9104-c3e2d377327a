﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Users.List;

namespace Witlab.Platform.Infrastructure.Platform.Queries;

/// <summary>
/// 列出用户查询服务实现
/// </summary>
public class ListUsersQueryService : IListUsersQueryService
{
  private readonly AppDbContext _dbContext;

  public ListUsersQueryService(AppDbContext dbContext)
  {
    _dbContext = dbContext;
  }

  public async Task<IEnumerable<UserDTO>> ListAsync(int? skip, int? take)
  {
    IQueryable<User> query = _dbContext.Users;

    if (skip.HasValue)
    {
      query = query.Skip(skip.Value);
    }

    if (take.HasValue)
    {
      query = query.Take(take.Value);
    }

    var users = await query
                    .Include(u => u.UserRoles).ThenInclude(l => l.Role)
                    .Include(u => u.UserDepts).ThenInclude(u => u.Dept)
                    .ToListAsync();

    return users.Select(user => new UserDTO(
        user.Id,
        user.UserName,
        user.FullName,
        user.Email,
        user.Phone,
        user.Address,
        user.Icon,
        user.Sex.Value,
        user.Sex.Name,
        user.State.Value,
        user.State.Name,
        user.Remark,
        user.UserRoles.Select(l => l.Role).Select(r => r.RoleCode).ToList() ?? [],
        user.UserDepts.Select(l => l.Dept).Select(r => r.DeptCode).ToList() ?? [],
        user.Created.DateTime,
        user.CreatedBy,
        user.LastModified.DateTime,
        user.LastModifiedBy
    ));
  }
}
