﻿using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 用户删除事件处理器
/// </summary>
public class UserDeptAssignedHandler : INotificationHandler<UserDeptAssignedEvent>
{
  private readonly ILogger<UserDeptAssignedHandler> _logger;
  private readonly IMediator _mediator;
  private readonly IWitLabSyncService _witLabSyncService;

  public UserDeptAssignedHandler(ILogger<UserDeptAssignedHandler> logger, IMediator mediator, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _mediator = mediator;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(UserDeptAssignedEvent notification, CancellationToken cancellationToken)
  {
    var syncResult = await _witLabSyncService.SyncUserDeptsAsync(notification.UserName, notification.DeptCodes, cancellationToken);
    if (syncResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, syncResult.Errors));

  }
}
