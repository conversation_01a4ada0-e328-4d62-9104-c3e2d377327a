﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <OutputType>Exe</OutputType>
    <WebProjectMode>true</WebProjectMode>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ListStartupServices" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.Result.AspNetCore" />
    <PackageReference Include="FastEndpoints" />
    <PackageReference Include="FastEndpoints.Swagger" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Serilog.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Witlab.Platform.Infrastructure\Witlab.Platform.Infrastructure.csproj" />
    <ProjectReference Include="..\Witlab.Platform.UseCases\Witlab.Platform.UseCases.csproj" />
    <ProjectReference Include="..\Witlab.Platform.ServiceDefaults\Witlab.Platform.ServiceDefaults.csproj" />
  </ItemGroup>

</Project>
