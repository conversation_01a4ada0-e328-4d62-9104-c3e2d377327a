﻿using Witlab.Platform.UseCases.Platform.Users.GetByName;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据用户名获取用户
/// </summary>
public class GetByName(IMediator _mediator) : Endpoint<GetUserByNameRequest, UserRecord>
{
  public override void Configure()
  {
    Get(GetUserByNameRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "根据用户名获取用户";
      s.Description = "根据用户名获取用户详细信息";
    });
  }

  public override async Task HandleAsync(GetUserByNameRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserByNameQuery(request.UserName);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new UserRecord(
          dto.Id,
          dto.UserName,
          dto.FullName,
          dto.Email,
          dto.Phone,
          dto.Address,
          dto.Icon,
          dto.SexValue,
          dto.SexName,
          dto.StateValue,
          dto.StateName,
          dto.Remark,
          dto.Roles,
          dto.Depts,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      );
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
