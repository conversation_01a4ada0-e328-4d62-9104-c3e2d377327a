﻿using Witlab.Platform.UseCases.Platform.Roles.Delete;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 删除角色
/// </summary>
public class Delete(IMediator _mediator) : Endpoint<DeleteRoleRequest>
{
  public override void Configure()
  {
    Delete(DeleteRoleRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "删除角色";
      s.Description = "根据角色ID删除角色";
    });
  }

  public override async Task HandleAsync(DeleteRoleRequest request, CancellationToken cancellationToken)
  {
    var command = new DeleteRoleCommand(request.RoleId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
