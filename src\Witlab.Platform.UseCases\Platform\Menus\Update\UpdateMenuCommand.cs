﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.UseCases.Platform.Menus.Update;

/// <summary>
/// 更新菜单命令
/// </summary>
public record UpdateMenuCommand(
    Guid MenuId,
    string MenuName,
    MenuType MenuType,
    string? AuthCode,
    Guid? ParentId,
    int OrderNum,
    string? Icon,
    string? ActiveIcon,
    string? Router,
    string? Component,
    string? RouterName,
    string? Redirect,
    Dictionary<string, string>? Query,
    string? Remark,
    string? Title,
    bool AffixTab,
    int? AffixTabOrder,
    string? Badge,
    string? BadgeType,
    string? BadgeVariants,
    string? IframeSrc,
    string? Link,
    bool? OpenInNewWindow,
    bool? KeepAlive,
    bool? HideInMenu,
    bool? HideInTab,
    bool? HideInBreadcrumb,
    bool? HideChildrenInMenu,
    string? ActivePath,
    int? MaxNumofOpenTab,
    bool? NoBasicLayout
) : ICommand<Result<MenuDTO>>;
