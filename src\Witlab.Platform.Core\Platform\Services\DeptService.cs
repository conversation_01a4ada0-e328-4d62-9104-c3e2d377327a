﻿using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 部门领域服务实现
/// </summary>
public class DeptService : IDeptService
{
  private readonly IRepository<Dept> _deptRepository;
  private readonly IRepository<User> _userRepository;
  private readonly IMediator _mediator;
  private readonly ILogger<DeptService> _logger;

  public DeptService(
      IRepository<Dept> deptRepository,
      IRepository<User> userRepository,
      IMediator mediator,
      ILogger<DeptService> logger)
  {
    _deptRepository = deptRepository;
    _userRepository = userRepository;
    _mediator = mediator;
    _logger = logger;
  }

  /// <inheritdoc />
  public async Task<Result<Dept>> CreateDeptAsync(string deptName, string deptCode, Guid? parentId = null, string? leader = null, int orderNum = 0, string? remark = null, bool status = true)
  {
    try
    {
      // 检查部门编码是否已存在
      var spec = new DeptByCodeSpec(deptCode);
      var existingDept = await _deptRepository.FirstOrDefaultAsync(spec);
      if (existingDept != null)
      {
        return Result.Error("部门编码已存在");
      }

      // 创建新部门
      Dept dept;
      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        // 检查父部门是否存在
        var parentDept = await _deptRepository.GetByIdAsync(parentId.Value);
        if (parentDept == null)
        {
          return Result.Error("父部门不存在");
        }
        dept = new Dept(parentId.Value, deptName, deptCode);
      }
      else
      {
        dept = new Dept(deptName, deptCode);
      }

      // 设置其他属性
      dept.Leader = leader;
      dept.OrderNum = orderNum;
      dept.Remark = remark;
      dept.State = status ? DeptState.Activate : DeptState.Deactivate;

      // 保存部门
      var createdDept = await _deptRepository.AddAsync(dept);

      // 发布部门创建事件
      //await _mediator.Publish(new DeptCreatedEvent(createdDept.Id, createdDept.DeptName));

      return Result.Success(createdDept);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建部门时发生错误: {Message}", ex.Message);
      return Result.Error($"创建部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Dept>> UpdateDeptAsync(Guid deptId, string deptName, string deptCode, Guid? parentId, string? leader = null, int orderNum = 0, string? remark = null, bool status = true)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      // 检查部门编码是否已被其他部门使用
      if (dept.DeptCode != deptCode)
      {
        var spec = new DeptByCodeSpec(deptCode);
        var existingDept = await _deptRepository.FirstOrDefaultAsync(spec);
        if (existingDept != null && existingDept.Id != deptId)
        {
          return Result.Error("部门编码已存在");
        }
      }

      // 检查父部门是否存在且不是自己
      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        if (parentId.Value == deptId)
        {
          return Result.Error("父部门不能是自己");
        }

        var parentDept = await _deptRepository.GetByIdAsync(parentId.Value);
        if (parentDept == null)
        {
          return Result.Error("父部门不存在");
        }

        // 检查是否会形成循环引用
        if (await IsChildDeptAsync(deptId, parentId.Value))
        {
          return Result.Error("不能将子部门设为父部门");
        }

        dept.ParentId = parentId.Value;
      }

      // 更新部门信息
      var deptType = dept.GetType();
      var deptNameProperty = deptType.GetProperty("DeptName");
      var deptCodeProperty = deptType.GetProperty("DeptCode");

      if (deptNameProperty != null && deptNameProperty.CanWrite)
      {
        deptNameProperty.SetValue(dept, Guard.Against.NullOrEmpty(deptName, nameof(deptName)));
      }

      if (deptCodeProperty != null && deptCodeProperty.CanWrite)
      {
        deptCodeProperty.SetValue(dept, Guard.Against.NullOrEmpty(deptCode, nameof(deptCode)));
      }

      dept.Leader = leader;
      dept.OrderNum = orderNum;
      dept.Remark = remark;
      dept.State = status ? DeptState.Activate : DeptState.Deactivate;

      await _deptRepository.UpdateAsync(dept);

      return Result.Success(dept);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新部门时发生错误: {Message}", ex.Message);
      return Result.Error($"更新部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeleteDeptAsync(Guid deptId)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      // 检查是否有子部门
      var childDepts = await GetChildDeptAsync(deptId);
      if (childDepts.IsSuccess && childDepts.Value.Count > 0)
      {
        return Result.Error("请先删除子部门");
      }

      // 检查是否有用户
      var deptUsers = await GetDeptUsersAsync(deptId);
      if (deptUsers.IsSuccess && deptUsers.Value.Count > 0)
      {
        return Result.Error("请先移除部门下的用户");
      }

      // 发布部门删除事件
      await _mediator.Publish(new DeptDeletedEvent(deptId, dept.DeptCode));
      await _deptRepository.DeleteAsync(dept);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除部门时发生错误: {Message}", ex.Message);
      return Result.Error($"删除部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Dept>> GetDeptAsync(Guid deptId)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      return Result.Success(dept);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取部门时发生错误: {Message}", ex.Message);
      return Result.Error($"获取部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Dept>> GetDeptByCodeAsync(string deptCode)
  {
    try
    {
      var spec = new DeptByCodeSpec(deptCode);
      var dept = await _deptRepository.FirstOrDefaultAsync(spec);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      return Result.Success(dept);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据编码获取部门时发生错误: {Message}", ex.Message);
      return Result.Error($"获取部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Dept>>> GetDeptTreeAsync(Guid? parentId = null)
  {
    try
    {
      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        // 检查父部门是否存在
        var parentDept = await _deptRepository.GetByIdAsync(parentId.Value);
        if (parentDept == null)
        {
          return Result.NotFound("父部门不存在");
        }

        // 获取指定父部门下的部门树
        var spec = new DeptsByParentIdSpec(parentId.Value);
        var depts = await _deptRepository.ListAsync(spec);
        return Result.Success(depts.ToList());
      }
      else
      {
        // 获取所有顶级部门
        var spec = new DeptsByParentIdSpec(Guid.Empty);
        var depts = await _deptRepository.ListAsync(spec);
        return Result.Success(depts.ToList());
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取部门树时发生错误: {Message}", ex.Message);
      return Result.Error($"获取部门树失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Dept>>> GetChildDeptAsync(Guid parentId)
  {
    try
    {
      var spec = new DeptsByParentIdSpec(parentId);
      var depts = await _deptRepository.ListAsync(spec);
      return Result.Success(depts.ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取子部门时发生错误: {Message}", ex.Message);
      return Result.Error($"获取子部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ActivateDeptAsync(Guid deptId)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      dept.State = DeptState.Activate;
      await _deptRepository.UpdateAsync(dept);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "激活部门时发生错误: {Message}", ex.Message);
      return Result.Error($"激活部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeactivateDeptAsync(Guid deptId)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      dept.State = DeptState.Deactivate;
      await _deptRepository.UpdateAsync(dept);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用部门时发生错误: {Message}", ex.Message);
      return Result.Error($"禁用部门失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> AssignUsersToDeptAsync(Guid deptId, List<Guid> userIds)
  {
    try
    {
      var dept = await _deptRepository.GetByIdAsync(deptId);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      // 清除现有用户关联
      dept.UserDepts.Clear();

      // 添加新的用户关联
      foreach (var userId in userIds)
      {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
        {
          return Result.Error($"用户ID {userId} 不存在");
        }

        dept.UserDepts.Add(new UserDept(userId, deptId));
      }

      await _deptRepository.UpdateAsync(dept);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配用户到部门时发生错误: {Message}", ex.Message);
      return Result.Error($"分配用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<User>>> GetDeptUsersAsync(Guid deptId)
  {
    try
    {
      var spec = new DeptWithUsersSpec(deptId);
      var dept = await _deptRepository.FirstOrDefaultAsync(spec);
      if (dept == null)
      {
        return Result.NotFound("部门不存在");
      }

      var users = dept.UserDepts.Select(ud => ud.User).ToList() ?? new List<User>();
      return Result.Success(users);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取部门用户时发生错误: {Message}", ex.Message);
      return Result.Error($"获取部门用户失败: {ex.Message}");
    }
  }

  // 辅助方法：检查是否是子部门
  private async Task<bool> IsChildDeptAsync(Guid parentId, Guid childId)
  {
    var childDepts = await GetChildDeptAsync(parentId);
    if (!childDepts.IsSuccess || childDepts.Value.Count == 0)
    {
      return false;
    }

    if (childDepts.Value.Any(d => d.Id == childId))
    {
      return true;
    }

    foreach (var dept in childDepts.Value)
    {
      if (await IsChildDeptAsync(dept.Id, childId))
      {
        return true;
      }
    }

    return false;
  }
}
