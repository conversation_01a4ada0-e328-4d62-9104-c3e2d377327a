﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Depts.Create;

/// <summary>
/// 创建部门命令处理器
/// </summary>
public class CreateDeptHandler : ICommandHandler<CreateDeptCommand, Result<DeptDTO>>
{
  private readonly IDeptService _deptService;

  public CreateDeptHandler(IDeptService deptService)
  {
    _deptService = deptService;
  }

  public async Task<Result<DeptDTO>> Handle(CreateDeptCommand request, CancellationToken cancellationToken)
  {
    var result = await _deptService.CreateDeptAsync(
        request.DeptName,
        request.DeptCode,
        request.ParentId,
        request.Leader,
        request.OrderNum,
        request.Remark,
        request.Status
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var dept = result.Value;

    return Result.Success(MapToDto(dept));
  }

  private static DeptDTO MapToDto(Dept dept)
  {
    return new DeptDTO(
        dept.Id,
        dept.DeptName,
        dept.DeptCode,
        dept.ParentId,
        dept.Leader,
        dept.OrderNum,
        dept.State.Value,
        dept.State.Name,
        dept.Remark,
        dept.Created.DateTime,
        dept.CreatedBy,
        dept.LastModified.DateTime,
        dept.LastModifiedBy
    );
  }
}
