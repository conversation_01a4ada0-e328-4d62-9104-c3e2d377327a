﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 列出权限请求验证器
/// </summary>
public class ListPermissionsValidator : Validator<ListPermissionsRequest>
{
  public ListPermissionsValidator()
  {
    RuleFor(x => x.Skip)
        .GreaterThanOrEqualTo(0).WithMessage("Skip必须大于或等于0")
        .When(x => x.Skip.HasValue);

    RuleFor(x => x.Take)
        .GreaterThan(0).WithMessage("Take必须大于0")
        .LessThanOrEqualTo(100).WithMessage("Take不能超过100")
        .When(x => x.Take.HasValue);

    RuleFor(x => x.TypeValue)
        .InclusiveBetween(0, 3).WithMessage("权限类型值必须在0-3之间")
        .When(x => x.TypeValue.HasValue);
  }
}
