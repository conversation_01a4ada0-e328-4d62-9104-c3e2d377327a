﻿using Witlab.Platform.UseCases.Platform.Permissions.Delete;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 删除权限
/// </summary>
public class Delete(IMediator _mediator) : Endpoint<DeletePermissionRequest>
{
  public override void Configure()
  {
    Delete(DeletePermissionRequest.Route);
    Description(x => x.AutoTagOverride("Permission"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "删除权限";
      s.Description = "根据权限ID删除权限";
    });
  }

  public override async Task HandleAsync(DeletePermissionRequest request, CancellationToken cancellationToken)
  {
    var command = new DeletePermissionCommand(request.PermissionId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
