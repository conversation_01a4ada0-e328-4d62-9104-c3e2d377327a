namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// 会话管理配置
/// </summary>
public class SessionSettings
{
  /// <summary>
  /// 是否启用会话跟踪
  /// </summary>
  public bool EnableSessionTracking { get; set; } = true;

  /// <summary>
  /// 最大并发会话数（0表示不限制）
  /// </summary>
  public int MaxConcurrentSessions { get; set; } = 5;

  /// <summary>
  /// 是否允许多设备登录
  /// </summary>
  public bool AllowMultipleDeviceLogin { get; set; } = true;

  /// <summary>
  /// 会话超时时间（分钟）
  /// </summary>
  public int SessionTimeoutMinutes { get; set; } = 30;

  /// <summary>
  /// 是否启用会话审计
  /// </summary>
  public bool EnableSessionAudit { get; set; } = false;

  /// <summary>
  /// 清理过期会话的间隔时间（分钟）
  /// </summary>
  public int CleanupIntervalMinutes { get; set; } = 60;

  /// <summary>
  /// 是否启用异常登录检测
  /// </summary>
  public bool EnableAnomalyDetection { get; set; } = false;

  /// <summary>
  /// IP地址变化检测
  /// </summary>
  public bool DetectIpChange { get; set; } = false;

  /// <summary>
  /// 设备变化检测
  /// </summary>
  public bool DetectDeviceChange { get; set; } = false;

  /// <summary>
  /// 会话活动更新间隔（秒）
  /// </summary>
  public int ActivityUpdateIntervalSeconds { get; set; } = 300; // 5分钟

  /// <summary>
  /// 是否记录详细的会话日志
  /// </summary>
  public bool EnableDetailedLogging { get; set; } = false;
}
