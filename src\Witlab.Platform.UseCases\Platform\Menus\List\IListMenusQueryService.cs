﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.UseCases.Platform.Menus.List;

/// <summary>
/// 列出菜单查询服务接口
/// </summary>
public interface IListMenusQueryService
{
  /// <summary>
  /// 列出菜单
  /// </summary>
  /// <param name="skip">跳过的记录数</param>
  /// <param name="take">获取的记录数</param>
  /// <param name="menuType">菜单类型</param>
  /// <returns>菜单列表</returns>
  Task<IEnumerable<MenuDTO>> ListAsync(int? skip = null, int? take = null, MenuType? menuType = null);
}
