﻿namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

public class CreatePermissionResponse
{
  public Guid Id { get; set; }
  public string Code { get; set; }
  public string Name { get; set; }
  public string? Description { get; set; }
  public int TypeValue { get; set; }
  public string TypeName { get; set; }

  public CreatePermissionResponse(Guid id, string code, string name, string? description, int typeValue, string typeName)
  {
    Id = id;
    Code = code;
    Name = name;
    Description = description;
    TypeValue = typeValue;
    TypeName = typeName;
  }
}
