﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.ValueObjects;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 登录接口
/// </summary>
public class Login(IAuthService _authService) : Endpoint<LoginRequest, LoginResponse>
{
  public override void Configure()
  {
    Post("/auth/login");
    Description(x => x.WithTags("Auth"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "用户登录";
      s.Description = "验证用户名和密码，返回访问令牌和刷新令牌";
      s.ExampleRequest = new LoginRequest { UserName = "admin", Password = "admin123" };
    });
  }

  public override async Task HandleAsync(LoginRequest request, CancellationToken cancellationToken)
  {
    // 创建会话信息
    var sessionInfo = CreateSessionInfo();

    var result = await _authService.LoginAsync(request.UserName!, request.Password!, sessionInfo, LoginSource.Web);

    if (result.IsSuccess)
    {
      var authResponse = result.Value;
      Response = new LoginResponse
      {
        AccessToken = authResponse.AccessToken,
        RefreshToken = authResponse.RefreshToken,
        ExpiresIn = authResponse.ExpiresIn,
        TokenType = authResponse.TokenType,
        UserId = authResponse.UserId,
        UserName = authResponse.UserName
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }

  /// <summary>
  /// 创建会话信息
  /// </summary>
  /// <returns>会话信息</returns>
  private SessionInfo CreateSessionInfo()
  {
    // 获取客户端IP地址
    var ipAddress = GetClientIpAddress();

    // 获取User-Agent
    var userAgent = HttpContext.Request.Headers.UserAgent.ToString();

    // 获取Accept-Language
    var acceptLanguage = HttpContext.Request.Headers.AcceptLanguage.ToString();

    return SessionInfo.FromHttpRequest(ipAddress, userAgent, acceptLanguage);
  }

  /// <summary>
  /// 获取客户端IP地址
  /// </summary>
  /// <returns>IP地址</returns>
  private string GetClientIpAddress()
  {
    // 检查X-Forwarded-For头（代理服务器）
    var forwardedFor = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
    if (!string.IsNullOrEmpty(forwardedFor))
    {
      // 取第一个IP地址
      var ips = forwardedFor.Split(',');
      if (ips.Length > 0)
      {
        return ips[0].Trim();
      }
    }

    // 检查X-Real-IP头
    var realIp = HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
    if (!string.IsNullOrEmpty(realIp))
    {
      return realIp;
    }

    // 使用RemoteIpAddress
    var remoteIp = HttpContext.Connection.RemoteIpAddress?.ToString();
    if (!string.IsNullOrEmpty(remoteIp))
    {
      // 如果是IPv6的本地回环地址，转换为IPv4
      if (remoteIp == "::1")
      {
        return "127.0.0.1";
      }
      return remoteIp;
    }

    return "Unknown";
  }
}
