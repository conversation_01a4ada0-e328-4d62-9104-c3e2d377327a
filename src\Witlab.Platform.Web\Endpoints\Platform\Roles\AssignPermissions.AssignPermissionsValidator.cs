﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 分配权限给角色请求验证器
/// </summary>
public class AssignPermissionsValidator : Validator<AssignPermissionsRequest>
{
  public AssignPermissionsValidator()
  {
    RuleFor(x => x.RoleId)
        .NotEmpty().WithMessage("角色ID不能为空");

    RuleFor(x => x.PermissionIds)
        .NotEmpty().WithMessage("权限ID列表不能为空");
  }
}
