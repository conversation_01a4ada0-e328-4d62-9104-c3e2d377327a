﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.AssignDepts;

/// <summary>
/// 分配部门给用户命令处理器
/// </summary>
public class AssignDeptsToUserHandler : ICommandHandler<AssignDeptsToUserCommand, Result>
{
  private readonly IUserService _userService;

  public AssignDeptsToUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(AssignDeptsToUserCommand request, CancellationToken cancellationToken)
  {
    return await _userService.AssignDeptsToUserAsync(request.UserId, request.DeptIds);
  }
}
