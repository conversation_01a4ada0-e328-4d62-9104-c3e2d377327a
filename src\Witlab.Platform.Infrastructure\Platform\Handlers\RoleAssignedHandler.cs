﻿using MediatR;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 角色分配事件处理器
/// </summary>
public class RoleAssignedHandler : INotificationHandler<RoleAssignedEvent>
{
  private readonly ILogger<RoleAssignedHandler> _logger;

  public RoleAssignedHandler(ILogger<RoleAssignedHandler> logger)
  {
    _logger = logger;
  }

  public Task Handle(RoleAssignedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理角色分配事件: 用户ID {UserId}, 角色ID {RoleId}",
        notification.UserId, notification.RoleId);

    // 这里可以添加角色分配后的业务逻辑
    return Task.CompletedTask;
  }
}
