﻿using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Witlab.Platform.Core.Common.Interfaces;

namespace Witlab.Platform.Infrastructure.Data.Interceptors;
public class AuditableEntityInterceptor : SaveChangesInterceptor
{
  private readonly IUser _user;
  private readonly TimeProvider _dateTime;

  public AuditableEntityInterceptor(
      IUser user,
      TimeProvider dateTime)
  {
    _user = user;
    _dateTime = dateTime;
  }

  public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
  {
    UpdateEntities(eventData.Context);

    return base.SavingChanges(eventData, result);
  }

  public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
  {
    UpdateEntities(eventData.Context);

    return base.SavingChangesAsync(eventData, result, cancellationToken);
  }

  private void UpdateEntities(DbContext? context)
  {
    if (context == null) return;

    var changeTrackerEntries = context.ChangeTracker.Entries()
        .Where(e => IsAuditableEntity(e.Entity.GetType()))
        .Cast<EntityEntry>();

    foreach (var entry in changeTrackerEntries)
    {
      if (entry.State is EntityState.Added or EntityState.Modified || entry.HasChangedOwnedEntities())
      {
        var utcNow = _dateTime.GetUtcNow();
        UpdateAuditableEntity(entry, utcNow);
      }
    }
  }

  private static bool IsAuditableEntity(Type type)
  {
    ArgumentNullException.ThrowIfNull(type);

    while (type != typeof(object))
    {
      if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(AuditableEntityBase<>))
        return true;
      if (type == typeof(AuditableEntityBase))
        return true;
      type = type.BaseType!;
    }
    return false;
  }

  private void UpdateAuditableEntity(EntityEntry entry, DateTimeOffset utcNow)
  {
    var entity = entry.Entity;
    if (entity == null) return;

    var type = entity.GetType();

    if (entry.State == EntityState.Added)
    {
      UpdateEntityCreation(entity, type, utcNow);
    }

    UpdateEntityModification(entity, type, utcNow);
  }

  private void UpdateEntityCreation(object entity, Type type, DateTimeOffset utcNow)
  {
    ArgumentNullException.ThrowIfNull(entity);
    ArgumentNullException.ThrowIfNull(type);

    while (type != typeof(object))
    {
      if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(AuditableEntityBase<>))
      {
        dynamic auditableEntity = entity;
        auditableEntity.CreatedBy = _user.Id;
        auditableEntity.Created = utcNow;
        break;
      }
      if (type == typeof(AuditableEntityBase))
      {
        var auditableEntity = (AuditableEntityBase)entity;
        auditableEntity.CreatedBy = _user.Id;
        auditableEntity.Created = utcNow;
        break;
      }
      type = type.BaseType!;
    }
  }

  private void UpdateEntityModification(object entity, Type type, DateTimeOffset utcNow)
  {
    ArgumentNullException.ThrowIfNull(entity);
    ArgumentNullException.ThrowIfNull(type);

    while (type != typeof(object))
    {
      if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(AuditableEntityBase<>))
      {
        dynamic auditableEntity = entity;
        auditableEntity.LastModifiedBy = _user.Id;
        auditableEntity.LastModified = utcNow;
        break;
      }
      if (type == typeof(AuditableEntityBase))
      {
        var auditableEntity = (AuditableEntityBase)entity;
        auditableEntity.LastModifiedBy = _user.Id;
        auditableEntity.LastModified = utcNow;
        break;
      }
      type = type.BaseType!;
    }
  }
}

public static class Extensions
{
  public static bool HasChangedOwnedEntities(this EntityEntry entry) =>
      entry.References.Any(r =>
          r.TargetEntry != null &&
          r.TargetEntry.Metadata.IsOwned() &&
          (r.TargetEntry.State == EntityState.Added || r.TargetEntry.State == EntityState.Modified));
}
