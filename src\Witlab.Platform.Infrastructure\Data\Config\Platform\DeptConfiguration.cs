﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class DeptConfiguration : IEntityTypeConfiguration<Dept>
{
  public void Configure(EntityTypeBuilder<Dept> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<Dept, Guid>();

    builder.Property(p => p.DeptName)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.DeptCode)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.Leader)
        .HasMaxLength(50);

    builder.Property(p => p.Remark)
        .HasMaxLength(500);

    // 配置DeptState SmartEnum转换
    builder.Property(p => p.State)
        .HasConversion(
            v => v.Value,
            v => DeptState.FromValue(v));

    // 忽略Users导航属性，避免自动创建外键关系
    //builder.Ignore(d => d.Users);

    // 不再使用外键约束，而是在应用程序代码中处理父子关系
    // 配置Children集合，但不设置外键约束
    builder.HasMany(e => e.Children)
        .WithOne()
        .HasForeignKey(e => e.ParentId)
        .IsRequired(false)  // 允许ParentId为null
        .OnDelete(DeleteBehavior.Restrict);

    // 忽略外键约束检查
    builder.HasIndex(e => e.ParentId)
        .HasDatabaseName("IX_Depts_ParentId");

    // 确保ParentId可以为null或Guid.Empty
    builder.Property(e => e.ParentId)
        .IsRequired(false);

    builder.ToTable("Depts");
  }
}
