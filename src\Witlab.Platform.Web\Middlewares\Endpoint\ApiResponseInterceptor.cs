﻿using System.Text.Json;
using System.Text.Json.Serialization;
using FluentValidation.Results;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.WebUtilities;

namespace Witlab.Platform.Web.Middlewares.Endpoint;

public class ApiResponseInterceptor : IResponseInterceptor
{
  public async Task InterceptResponseAsync(object response, int statusCode, HttpContext ctx, IReadOnlyCollection<ValidationFailure> failures, CancellationToken ct)
  {
    var httpCtx = ctx;

    // 获取异常或错误对象(可自定义)
    object? error = null;

    // 一般情况下没有异常，某些中间件会在Items或Features里放异常
    var feature = httpCtx.Features.Get<IExceptionHandlerFeature>();
    if (feature?.Error != null || httpCtx.Items.ContainsKey("Error"))
    {
      //error = context.ExceptionDispatchInfo?.SourceException.Message ?? feature?.Error.Message ?? httpCtx.Items["Error"];

      error = feature?.Error.Message ?? httpCtx.Items["Error"];
    }

    var apiResponse = new ApiResponse<object>
    {
      Code = statusCode,
      Data = error == null ? response : default,
      Error = error,
      Message = ReasonPhrases.GetReasonPhrase(statusCode)
    };

    await httpCtx.Response.Body.FlushAsync();

    var json = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions()
    {
      PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
      DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    });
    await httpCtx.Response.BodyWriter.WriteAsync(System.Text.Encoding.UTF8.GetBytes(json), ct);
  }
}
