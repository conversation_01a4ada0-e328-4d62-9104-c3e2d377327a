﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 权限类型
/// </summary>
public class PermissionType : SmartEnum<PermissionType>
{
  /// <summary>
  /// API接口权限
  /// </summary>
  public static readonly PermissionType Api = new(nameof(Api), 0);

  /// <summary>
  /// 页面元素权限
  /// </summary>
  public static readonly PermissionType Element = new(nameof(Element), 1);

  /// <summary>
  /// 数据权限
  /// </summary>
  public static readonly PermissionType Data = new(nameof(Data), 2);

  protected PermissionType(string name, int value) : base(name, value) { }
}
