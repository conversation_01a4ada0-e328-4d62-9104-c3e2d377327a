<Solution>
  <Configurations>
    <Platform Name="Any CPU" />
    <Platform Name="x64" />
    <Platform Name="x86" />
  </Configurations>
  <Folder Name="/Solution Items/">
    <File Path=".editorconfig" />
    <File Path="CleanArchitecture.nuspec" />
    <File Path="Directory.Build.props" />
    <File Path="Directory.Packages.props" />
  </Folder>
  <Folder Name="/src/">
    <Project Path="src/Witlab.Platform.AspireHost/Witlab.Platform.AspireHost.csproj" />
    <Project Path="src/Witlab.Platform.Core/Witlab.Platform.Core.csproj" />
    <Project Path="src/Witlab.Platform.Infrastructure/Witlab.Platform.Infrastructure.csproj" />
    <Project Path="src/Witlab.Platform.ServiceDefaults/Witlab.Platform.ServiceDefaults.csproj" />
    <Project Path="src/Witlab.Platform.UseCases/Witlab.Platform.UseCases.csproj" />
    <Project Path="src/Witlab.Platform.Web/Witlab.Platform.Web.csproj" />
  </Folder>
  <Folder Name="/tests/">
    <Project Path="tests/Witlab.Platform.FunctionalTests/Witlab.Platform.FunctionalTests.csproj">
      <BuildDependency Project="src/Witlab.Platform.Web/Witlab.Platform.Web.csproj" />
    </Project>
    <Project Path="tests/Witlab.Platform.IntegrationTests/Witlab.Platform.IntegrationTests.csproj">
      <BuildDependency Project="src/Witlab.Platform.Infrastructure/Witlab.Platform.Infrastructure.csproj" />
    </Project>
    <Project Path="tests/Witlab.Platform.UnitTests/Witlab.Platform.UnitTests.csproj" />
  </Folder>
</Solution>
