﻿using Ardalis.Result;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.ValueObjects;
using Witlab.Platform.Infrastructure.Auth;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.UnitTests.Infrastructure.Auth;

/// <summary>
/// RefreshToken 时 Session 更新的单元测试
/// </summary>
public class RefreshTokenSessionUpdateTests
{
  private readonly Mock<ILogger<IAuthService>> _mockLogger;
  private readonly Mock<IUserService> _mockUserService;
  private readonly Mock<IPermissionService> _mockPermissionService;
  private readonly Mock<IJwtService> _mockJwtService;
  private readonly Mock<IRefreshTokenService> _mockRefreshTokenService;
  private readonly Mock<IUserSessionService> _mockUserSessionService;
  private readonly Mock<IOptions<JwtSettings>> _mockJwtSettings;
  private readonly Mock<IOptions<SessionSettings>> _mockSessionSettings;
  private readonly AuthService _authService;

  public RefreshTokenSessionUpdateTests()
  {
    _mockLogger = new Mock<ILogger<IAuthService>>();
    _mockUserService = new Mock<IUserService>();
    _mockPermissionService = new Mock<IPermissionService>();
    _mockJwtService = new Mock<IJwtService>();
    _mockRefreshTokenService = new Mock<IRefreshTokenService>();
    _mockUserSessionService = new Mock<IUserSessionService>();
    _mockJwtSettings = new Mock<IOptions<JwtSettings>>();
    _mockSessionSettings = new Mock<IOptions<SessionSettings>>();

    var jwtSettings = new JwtSettings
    {
      AccessTokenExpirationMinutes = 30,
      RefreshTokenExpirationDays = 7
    };

    var sessionSettings = new SessionSettings
    {
      EnableSessionTracking = true
    };

    _mockJwtSettings.Setup(x => x.Value).Returns(jwtSettings);
    _mockSessionSettings.Setup(x => x.Value).Returns(sessionSettings);

    _authService = new AuthService(
      _mockLogger.Object,
      _mockUserService.Object,
      _mockPermissionService.Object,
      _mockJwtService.Object,
      _mockRefreshTokenService.Object,
      _mockUserSessionService.Object,
      _mockJwtSettings.Object,
      _mockSessionSettings.Object);
  }

  [Fact]
  public async Task RefreshTokenAsync_ShouldUpdateSessionToken_WhenSessionTrackingEnabled()
  {
    // Arrange
    var userId = Guid.NewGuid();
    var oldAccessToken = "old-access-token";
    var oldRefreshToken = "old-refresh-token";
    var newAccessToken = "new-access-token";
    var newRefreshToken = "new-refresh-token";
    var oldTokenId = "old-jti";
    var newTokenId = "new-jti";

    var user = new User("testuser", "hashedpassword", "testuser", "<EMAIL>");
    var refreshToken = new RefreshToken(userId, oldRefreshToken, DateTime.UtcNow.AddDays(7));
    var roles = new List<Role>();

    // Setup mocks
    _mockJwtService.Setup(x => x.IsInBlacklistAsync(oldAccessToken))
      .ReturnsAsync(false);

    _mockJwtService.Setup(x => x.GetUserIdFromToken(oldAccessToken))
      .Returns(userId);

    _mockJwtService.Setup(x => x.GetJtiFromToken(oldAccessToken))
      .Returns(oldTokenId);

    _mockJwtService.Setup(x => x.GetJtiFromToken(newAccessToken))
      .Returns(newTokenId);

    _mockJwtService.Setup(x => x.GenerateAccessToken(user, roles, null, null))
      .Returns(newAccessToken);

    _mockJwtService.Setup(x => x.GenerateRefreshToken())
      .Returns(newRefreshToken);

    _mockRefreshTokenService.Setup(x => x.GetRefreshTokenAsync(oldRefreshToken))
      .ReturnsAsync(Result<RefreshToken?>.Success(refreshToken));

    _mockRefreshTokenService.Setup(x => x.UseRefreshTokenAsync(oldRefreshToken))
      .ReturnsAsync(Result.Success());

    _mockRefreshTokenService.Setup(x => x.CreateRefreshTokenAsync(userId, newRefreshToken, It.IsAny<DateTime>()))
      .ReturnsAsync(Result<RefreshToken>.Success(refreshToken));

    _mockUserService.Setup(x => x.GetUserAsync(userId))
      .ReturnsAsync(Result<User>.Success(user));

    _mockUserService.Setup(x => x.GetUserRolesAsync(userId))
      .ReturnsAsync(Result<List<Role>>.Success(roles));

    _mockUserSessionService.Setup(x => x.UpdateSessionTokenAsync(oldTokenId, newTokenId, It.IsAny<DateTime>()))
      .ReturnsAsync(Result.Success());

    // Act
    var result = await _authService.RefreshTokenAsync(oldAccessToken, oldRefreshToken, null, null);

    // Assert
    Assert.True(result.IsSuccess);
    Assert.Equal(newAccessToken, result.Value.AccessToken);
    Assert.Equal(newRefreshToken, result.Value.RefreshToken);

    // Verify that UpdateSessionTokenAsync was called with correct parameters
    _mockUserSessionService.Verify(
      x => x.UpdateSessionTokenAsync(oldTokenId, newTokenId, It.IsAny<DateTime>()),
      Times.Once);
  }

  [Fact]
  public async Task RefreshTokenAsync_ShouldNotUpdateSession_WhenSessionTrackingDisabled()
  {
    // Arrange
    var sessionSettings = new SessionSettings
    {
      EnableSessionTracking = false
    };
    _mockSessionSettings.Setup(x => x.Value).Returns(sessionSettings);

    var authService = new AuthService(
      _mockLogger.Object,
      _mockUserService.Object,
      _mockPermissionService.Object,
      _mockJwtService.Object,
      _mockRefreshTokenService.Object,
      _mockUserSessionService.Object,
      _mockJwtSettings.Object,
      _mockSessionSettings.Object);

    var userId = Guid.NewGuid();
    var oldAccessToken = "old-access-token";
    var oldRefreshToken = "old-refresh-token";
    var newAccessToken = "new-access-token";
    var newRefreshToken = "new-refresh-token";

    var user = new User("testuser", "hashedpassword", "testuser", "<EMAIL>");
    var refreshToken = new RefreshToken(userId, oldRefreshToken, DateTime.UtcNow.AddDays(7));
    var roles = new List<Role>();

    // Setup mocks (similar to previous test but without session update expectations)
    _mockJwtService.Setup(x => x.IsInBlacklistAsync(oldAccessToken))
      .ReturnsAsync(false);

    _mockJwtService.Setup(x => x.GetUserIdFromToken(oldAccessToken))
      .Returns(userId);

    _mockJwtService.Setup(x => x.GenerateAccessToken(user, roles, null, null))
      .Returns(newAccessToken);

    _mockJwtService.Setup(x => x.GenerateRefreshToken())
      .Returns(newRefreshToken);

    _mockRefreshTokenService.Setup(x => x.GetRefreshTokenAsync(oldRefreshToken))
      .ReturnsAsync(Result<RefreshToken?>.Success(refreshToken));

    _mockRefreshTokenService.Setup(x => x.UseRefreshTokenAsync(oldRefreshToken))
      .ReturnsAsync(Result.Success());

    _mockRefreshTokenService.Setup(x => x.CreateRefreshTokenAsync(userId, newRefreshToken, It.IsAny<DateTime>()))
      .ReturnsAsync(Result<RefreshToken>.Success(refreshToken));

    _mockUserService.Setup(x => x.GetUserAsync(userId))
      .ReturnsAsync(Result<User>.Success(user));

    _mockUserService.Setup(x => x.GetUserRolesAsync(userId))
      .ReturnsAsync(Result<List<Role>>.Success(roles));

    // Act
    var result = await authService.RefreshTokenAsync(oldAccessToken, oldRefreshToken, null, null);

    // Assert
    Assert.True(result.IsSuccess);

    // Verify that UpdateSessionTokenAsync was NOT called
    _mockUserSessionService.Verify(
      x => x.UpdateSessionTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime>()),
      Times.Never);
  }
}
