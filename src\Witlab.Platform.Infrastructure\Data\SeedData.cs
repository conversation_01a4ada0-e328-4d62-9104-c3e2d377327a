﻿using Witlab.Platform.Core.ContributorAggregate;
using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data;

public static class SeedData
{
  public static readonly Contributor Contributor1 = new("<PERSON>rda<PERSON>");
  public static readonly Contributor Contributor2 = new("Snowfrog");

  // 系统管理模块菜单
  public static readonly Guid SystemMenuId = Guid.Parse("A0001000-0000-0000-0000-000000000000");
  public static readonly Guid SystemUserMenuId = Guid.Parse("A0001001-0000-0000-0000-000000000000");
  public static readonly Guid SystemRoleMenuId = Guid.Parse("A0001002-0000-0000-0000-000000000000");
  public static readonly Guid SystemMenuMenuId = Guid.Parse("A0001003-0000-0000-0000-000000000000");
  public static readonly Guid SystemDeptMenuId = Guid.Parse("A0001004-0000-0000-0000-000000000000");

  public static async Task InitializeAsync(AppDbContext dbContext)
  {
    //if (!await dbContext.Contributors.AnyAsync())
    //{
    //  await PopulateTestDataAsync(dbContext);
    //}

    if (!await dbContext.Menus.AnyAsync())
    {
      await SeedMenusAsync(dbContext);
    }

    if (!await dbContext.Permissions.AnyAsync())
    {
      await SeedPermissionsAsync(dbContext);
    }
  }

  public static async Task PopulateTestDataAsync(AppDbContext dbContext)
  {
    dbContext.Contributors.AddRange([Contributor1, Contributor2]);
    await dbContext.SaveChangesAsync();
  }

  public static async Task SeedMenusAsync(AppDbContext dbContext)
  {
    try
    {
      #region dashboard
      var dashboardMenu = new Menu()
      {
        MenuName = "概览",
        RouterName = "Dashboard",
        Router = "/dashboard",
        MenuType = MenuType.Catalog,
        OrderNum = -1,
        Title = "page.dashboard.title",
        Children = [
          new Menu()
          {
            MenuName = "分析页",
            RouterName = "Analytics",
            Router = "/analytics",
            Component = "/dashboard/analytics/index",
            AffixTab = true,
            Title = "page.dashboard.analytics"
          },
          new Menu()
          {
            MenuName = "工作台",
            RouterName = "Workspace",
            Router = "/workspace",
            Component = "/dashboard/workspace/index",
            Title = "page.dashboard.workspace"
          }
        ]
      };
      dbContext.Menus.Add(dashboardMenu);
      #endregion

      #region 系统管理模块
      var systemMenu = new Menu
      {
        Id = SystemMenuId,
        MenuName = "系统管理",
        MenuType = MenuType.Catalog,
        ParentId = null, // 根菜单，没有父菜单
        OrderNum = 9997,
        MenuIcon = "ion:settings-outline",
        Title = "system.title",
        Router = "/system",
        RouterName = "System",
        Component = null,
        State = MenuState.Activate,
        AffixTab = false
      };

      // 先添加并保存父菜单
      dbContext.Menus.Add(systemMenu);
      await dbContext.SaveChangesAsync();

      // 用户管理
      var userMenu = new Menu
      {
        Id = SystemUserMenuId,
        MenuName = "用户管理",
        MenuType = MenuType.Menu,
        ParentId = SystemMenuId,
        OrderNum = 1,
        MenuIcon = "mdi:account",
        Title = "system.user.title",
        Router = "/system/user",
        RouterName = "SystemUser",
        Component = "/system/user/list.vue",
        State = MenuState.Activate,
        AffixTab = false
      };

      // 角色管理
      var roleMenu = new Menu
      {
        Id = SystemRoleMenuId,
        MenuName = "角色管理",
        MenuType = MenuType.Menu,
        ParentId = SystemMenuId,
        OrderNum = 2,
        MenuIcon = "mdi:account-group",
        Title = "system.role.title",
        Router = "/system/role",
        RouterName = "SystemRole",
        Component = "/system/role/list.vue",
        State = MenuState.Activate,
        AffixTab = false
      };

      // 菜单管理
      var menuMenu = new Menu
      {
        Id = SystemMenuMenuId,
        MenuName = "菜单管理",
        MenuType = MenuType.Menu,
        ParentId = SystemMenuId,
        OrderNum = 3,
        MenuIcon = "mdi:menu",
        Title = "system.menu.title",
        Router = "/system/menu",
        RouterName = "SystemMenu",
        Component = "/system/menu/list.vue",
        State = MenuState.Activate,
        AffixTab = false
      };

      // 部门管理
      var deptMenu = new Menu
      {
        Id = SystemDeptMenuId,
        MenuName = "部门管理",
        MenuType = MenuType.Menu,
        ParentId = SystemMenuId,
        OrderNum = 4,
        MenuIcon = "charm:organisation",
        Title = "system.dept.title",
        Router = "/system/dept",
        RouterName = "SystemDept",
        Component = "/system/dept/list.vue",
        State = MenuState.Activate,
        AffixTab = false
      };

      // 添加子菜单
      dbContext.Menus.AddRange(userMenu, roleMenu, menuMenu, deptMenu);

      #endregion

      await dbContext.SaveChangesAsync();
    }
    catch (Exception ex)
    {
      // 记录异常信息，便于调试
      Console.WriteLine($"添加菜单种子数据时出错: {ex.Message}");
      if (ex.InnerException != null)
      {
        Console.WriteLine($"内部异常: {ex.InnerException.Message}");
      }
      throw; // 重新抛出异常，让调用者知道发生了错误
    }
  }

  public static async Task SeedPermissionsAsync(AppDbContext dbContext)
  {
    try
    {
      // 会话管理权限
      var sessionViewPermission = new Permission("session:view", "查看会话", PermissionType.Api)
      {
        Description = "允许查看用户会话列表和统计信息"
      };

      var sessionManagePermission = new Permission("session:manage", "管理会话", PermissionType.Api)
      {
        Description = "允许强制下线用户会话和管理会话"
      };

      dbContext.Permissions.AddRange(sessionViewPermission, sessionManagePermission);
      await dbContext.SaveChangesAsync();
    }
    catch (Exception ex)
    {
      // 记录异常信息，便于调试
      Console.WriteLine($"添加权限种子数据时出错: {ex.Message}");
      if (ex.InnerException != null)
      {
        Console.WriteLine($"内部异常: {ex.InnerException.Message}");
      }
      throw; // 重新抛出异常，让调用者知道发生了错误
    }
  }
}
