﻿using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Menus.GetRoutes;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 获取菜单路由
/// </summary>
public class GetRoutes : EndpointWithoutRequest<List<MenuRouteResponse>>
{
  private readonly IMediator _mediator;

  public GetRoutes(IMediator mediator)
  {
    _mediator = mediator;
  }

  public override void Configure()
  {
    Get("/platform/menus/routes");
    Description(x => x.AutoTagOverride("Menu"));
    //AllowAnonymous();
    //ResponseCache(30);
    Summary(s =>
    {
      s.Summary = "获取菜单路由";
      s.Description = "获取所有菜单的路由信息，用于前端路由配置";
      s.ResponseExamples[200] = new List<MenuRouteResponse>
        {
            new MenuRouteResponse
            {
                Path = "/system",
                Name = "System",
                Meta = new MenuMetaResponse
                {
                    Icon = "ion:settings-outline",
                    Title = "system.title",
                    Order = 9997
                },
                Children = new List<MenuRouteResponse>
                {
                    new MenuRouteResponse
                    {
                        Path = "/system/user",
                        Name = "SystemUser",
                        Component = "#/views/system/user/list.vue",
                        Meta = new MenuMetaResponse
                        {
                            Icon = "mdi:account",
                            Title = "system.user.title"
                        }
                    }
                }
            }
        };
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var query = new GetMenuRoutesQuery();
    var result = await _mediator.Send(query, cancellationToken);

    if (!result.IsSuccess)
    {
      await SendErrorsAsync(cancellation: cancellationToken);
      return;
    }

    Response = result.Value.Select(MapToResponse).ToList();

    await SendAsync(Response, cancellation: cancellationToken);
  }

  private static MenuRouteResponse MapToResponse(MenuRouteDTO dto)
  {
    var response = new MenuRouteResponse
    {
      Id = dto.Id,
      Name = dto.Name,
      Path = dto.Path,
      Type = dto.Type,
      Status = dto.Status,
      Pid = dto.Pid,
      Component = dto.Component,
      AuthCode = dto.AuthCode,
      Meta = new MenuMetaResponse
      {
        Icon = dto.Meta.Icon,
        ActiveIcon = dto.Meta.ActiveIcon,
        Title = dto.Meta.Title,
        Order = dto.Meta.Order,
        AffixTab = dto.Meta.AffixTab,
        AffixTabOrder = dto.Meta.AffixTabOrder,
        Badge = dto.Meta.Badge,
        BadgeType = dto.Meta.BadgeType,
        BadgeVariants = dto.Meta.BadgeVariants,
        IframeSrc = dto.Meta.IframeSrc,
        Link = dto.Meta.Link,
        OpenInNewWindow = dto.Meta.OpenInNewWindow,
        KeepAlive = dto.Meta.KeepAlive,
        HideInMenu = dto.Meta.HideInMenu,
        HideInTab = dto.Meta.HideInTab,
        HideInBreadcrumb = dto.Meta.HideInBreadcrumb,
        HideChildrenInMenu = dto.Meta.HideChildrenInMenu,
        Query = dto.Meta.Query,
        ActivePath = dto.Meta.ActivePath,
        MaxNumofOpenTab = dto.Meta.MaxNumofOpenTab,
        NoBasicLayout = dto.Meta.NoBasicLayout,
        Authority = dto.Meta.Authority,
      }
    };

    if (dto.Children != null && dto.Children.Count > 0)
    {
      response.Children = dto.Children.Select(MapToResponse).ToList();
    }

    return response;
  }
}
