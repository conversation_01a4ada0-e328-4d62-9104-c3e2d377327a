﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Depts.List;

namespace Witlab.Platform.Infrastructure.Platform.Queries;

/// <summary>
/// 列出部门查询服务实现
/// </summary>
public class ListDeptsQueryService : IListDeptsQueryService
{
  private readonly AppDbContext _dbContext;

  public ListDeptsQueryService(AppDbContext dbContext)
  {
    _dbContext = dbContext;
  }

  public async Task<IEnumerable<DeptDTO>> ListAsync(int? skip = null, int? take = null)
  {
    IQueryable<Dept> query = _dbContext.Depts;

    if (skip.HasValue)
    {
      query = query.Skip(skip.Value);
    }

    if (take.HasValue)
    {
      query = query.Take(take.Value);
    }

    var depts = await query.ToListAsync();

    return depts.Select(dept => new DeptDTO(
        dept.Id,
        dept.DeptName,
        dept.DeptCode,
        dept.ParentId,
        dept.Leader,
        dept.OrderNum,
        dept.State.Value,
        dept.State.Name,
        dept.Remark,
        dept.Created.DateTime,
        dept.CreatedBy,
        dept.LastModified.DateTime,
        dept.LastModifiedBy
    ));
  }
}
