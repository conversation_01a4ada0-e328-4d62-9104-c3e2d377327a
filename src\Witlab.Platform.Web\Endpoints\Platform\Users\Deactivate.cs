using Witlab.Platform.UseCases.Platform.Users.Deactivate;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 禁用用户
/// </summary>
[RequirePermission("user:deactivate")]
public class Deactivate(IMediator _mediator) : Endpoint<DeactivateUserRequest>
{
  public override void Configure()
  {
    Post(DeactivateUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "禁用用户";
      s.Description = "禁用指定用户的账号";
    });
  }

  public override async Task HandleAsync(DeactivateUserRequest request, CancellationToken cancellationToken)
  {
    var command = new DeactivateUserCommand(request.UserId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
