﻿using Witlab.Platform.UseCases.Platform.Users.Register;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 用户注册接口
/// </summary>
public class Register(IMediator _mediator) : Endpoint<RegisterRequest, RegisterResponse>
{
  public override void Configure()
  {
    Post("/auth/register");
    Description(x => x.WithTags("Auth"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "用户注册";
      s.Description = "创建新用户账号，需要提供用户名、密码和姓名";
      s.ExampleRequest = new RegisterRequest { UserName = "newuser", Password = "Password123!", FullName = "新用户" };
    });
  }

  public override async Task HandleAsync(RegisterRequest request, CancellationToken cancellationToken)
  {
    var command = new RegisterCommand(
        request.UserName!,
        request.Password!,
        request.FullName!,
        request.Email,
        request.VerificationCode
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new RegisterResponse
      {
        UserId = dto.Id,
        UserName = dto.UserName,
        FullName = dto.FullName,
        Email = dto.Email,
        Message = "注册成功"
      };
      return;
    }

    // 处理错误
    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
