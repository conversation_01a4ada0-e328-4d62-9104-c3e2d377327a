﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Menus.Get;

/// <summary>
/// 获取菜单查询处理器
/// </summary>
public class GetMenuHandler : IQueryHandler<GetMenuQuery, Result<MenuDTO>>
{
  private readonly IMenuService _menuService;

  public GetMenuHandler(IMenuService menuService)
  {
    _menuService = menuService;
  }

  public async Task<Result<MenuDTO>> Handle(GetMenuQuery request, CancellationToken cancellationToken)
  {
    var result = await _menuService.GetMenuAsync(request.MenuId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var menu = result.Value;
    return Result.Success(MapToDto(menu));
  }

  private static MenuDTO MapToDto(Menu menu)
  {
    return new MenuDTO(
        menu.Id,
        menu.MenuName,
        menu.ParentId,
        menu.MenuType.Value,
        menu.MenuType.Name,
        menu.OrderNum,
        menu.State.Value,
        menu.State.Name,
        menu.MenuIcon,
        menu.ActiveIcon,
        menu.Router,
        menu.RouterName,
        menu.Redirect,
        menu.Component,
        menu.Query,
        menu.Remark,
        menu.Title,
        menu.AffixTab,
        menu.AffixTabOrder,
        menu.Badge,
        menu.BadgeType,
        menu.BadgeVariants,
        menu.IframeSrc,
        menu.Link,
        menu.OpenInNewWindow,
        menu.KeepAlive,
        menu.HideInMenu,
        menu.HideInTab,
        menu.HideInBreadcrumb,
        menu.HideChildrenInMenu,
        menu.ActivePath,
        menu.MaxNumOfOpenTab,
        menu.NoBasicLayout,
        menu.Created.DateTime,
        menu.CreatedBy,
        menu.LastModified.DateTime,
        menu.LastModifiedBy
    );
  }
}
