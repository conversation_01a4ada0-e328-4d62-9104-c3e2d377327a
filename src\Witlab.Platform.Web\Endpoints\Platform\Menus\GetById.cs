﻿using Witlab.Platform.UseCases.Platform.Menus.Get;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 根据ID获取菜单
/// </summary>
[RequirePermission("menu:view")]
public class GetById(IMediator _mediator) : Endpoint<GetMenuByIdRequest, MenuRecord>
{
  public override void Configure()
  {
    Get(GetMenuByIdRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    Summary(s =>
    {
      s.Summary = "根据ID获取菜单";
      s.Description = "根据菜单ID获取菜单详细信息";
    });
  }

  public override async Task HandleAsync(GetMenuByIdRequest request, CancellationToken cancellationToken)
  {
    var query = new GetMenuQuery(request.MenuId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new MenuRecord(
        dto.Id,
        dto.MenuName,
        dto.ParentId,
        dto.MenuTypeValue,
        dto.MenuTypeName,
        dto.OrderNum,
        dto.StateValue,
        dto.StateName,
        dto.MenuIcon,
        dto.ActiveIcon,
        dto.Router,
        dto.RouterName,
        dto.Component,
        dto.Query,
        dto.Remark,
        dto.Title,
        dto.AffixTab,
        dto.AffixTabOrder,
        dto.Badge,
        dto.BadgeType,
        dto.BadgeVariants,
        dto.IframeSrc,
        dto.Link,
        dto.OpenInNewWindow,
        dto.KeepAlive,
        dto.HideInMenu,
        dto.HideInTab,
        dto.HideInBreadcrumb,
        dto.HideChildrenInMenu,
        dto.CreatedOnUtc,
        dto.CreatedBy,
        dto.LastModifiedOnUtc,
        dto.LastModifiedBy
      );
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
