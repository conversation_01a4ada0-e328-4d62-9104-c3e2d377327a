﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.AssignRolesByCodes;

/// <summary>
/// 根据角色编码分配角色给用户命令处理器
/// </summary>
public class AssignRolesToUserByCodesHandler : ICommandHandler<AssignRolesToUserByCodesCommand, Result>
{
  private readonly IUserService _userService;

  public AssignRolesToUserByCodesHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(AssignRolesToUserByCodesCommand request, CancellationToken cancellationToken)
  {
    return await _userService.AssignRolesToUserAsync(request.UserId, request.RoleCodes);
  }
}
