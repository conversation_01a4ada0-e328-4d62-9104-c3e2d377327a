﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Permissions.Create;

/// <summary>
/// 创建权限命令处理器
/// </summary>
public class CreatePermissionHandler : ICommandHandler<CreatePermissionCommand, Result<PermissionDTO>>
{
  private readonly IPermissionService _permissionService;

  public CreatePermissionHandler(IPermissionService permissionService)
  {
    _permissionService = permissionService;
  }

  public async Task<Result<PermissionDTO>> Handle(CreatePermissionCommand request, CancellationToken cancellationToken)
  {
    var result = await _permissionService.CreatePermissionAsync(
        request.Code,
        request.Name,
        request.Type,
        request.Description,
        request.MenuId
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var permission = result.Value;
    return Result.Success(MapToDto(permission));
  }

  private static PermissionDTO MapToDto(Permission permission)
  {
    return new PermissionDTO(
        permission.Id,
        permission.Code,
        permission.Name,
        permission.Description,
        permission.Type.Value,
        permission.Type.Name,
        permission.IsEnabled,
        permission.Created.DateTime,
        permission.CreatedBy,
        permission.LastModified.DateTime,
        permission.LastModifiedBy
    );
  }
}
