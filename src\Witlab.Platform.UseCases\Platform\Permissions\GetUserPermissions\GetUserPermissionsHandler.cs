﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Permissions.GetUserPermissions;

/// <summary>
/// 获取用户菜单路由查询处理器
/// </summary>
public class GetUserPermissionsHandler : IQueryHandler<GetUserPermissionsQuery, Result<List<PermissionDTO>>>
{
  private readonly IPermissionService _permissionService;

  public GetUserPermissionsHandler(IPermissionService permissionService)
  {
    _permissionService = permissionService;
  }

  public async Task<Result<List<PermissionDTO>>> Handle(GetUserPermissionsQuery request, CancellationToken cancellationToken)
  {
    var result = await _permissionService.GetUserPermissionsAsync(request.UserId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var userPermissions = result.Value.Select(MapToDto).ToList();
    return Result.Success(userPermissions);
  }

  private static PermissionDTO MapToDto(Permission permission)
  {
    return new PermissionDTO(
        permission.Id,
        permission.Code,
        permission.Name,
        permission.Description,
        permission.Type.Value,
        permission.Type.Name,
        permission.IsEnabled,
        permission.Created.DateTime,
        permission.CreatedBy,
        permission.LastModified.DateTime,
        permission.LastModifiedBy
    );
  }
}
