﻿namespace Witlab.Platform.Web.Endpoints.Auth.Models;

/// <summary>
/// 用户注册响应
/// </summary>
public class RegisterResponse
{
  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; set; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; set; } = string.Empty;

  /// <summary>
  /// 姓名
  /// </summary>
  public string FullName { get; set; } = string.Empty;

  /// <summary>
  /// 邮箱
  /// </summary>
  public string? Email { get; set; }

  /// <summary>
  /// 消息
  /// </summary>
  public string Message { get; set; } = string.Empty;
}
