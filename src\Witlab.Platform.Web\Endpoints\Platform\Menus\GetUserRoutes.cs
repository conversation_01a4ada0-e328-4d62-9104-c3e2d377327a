﻿using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Menus.GetUserRoutes;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 获取用户菜单路由
/// </summary>
public class GetUserRoutes : EndpointWithoutRequest<List<MenuRouteResponse>>
{
  private readonly IMediator _mediator;
  private readonly IUser _currentUser;

  public GetUserRoutes(IMediator mediator, IUser currentUser)
  {
    _mediator = mediator;
    _currentUser = currentUser;
  }

  public override void Configure()
  {
    Get("/platform/menus/user-routes");
    Description(x => x.AutoTagOverride("Menu"));
    Summary(s =>
    {
      s.Summary = "获取当前用户的菜单路由";
      s.Description = "获取当前登录用户的菜单路由信息，用于前端路由配置";
      s.ResponseExamples[200] = new List<MenuRouteResponse>
        {
                new MenuRouteResponse
                {
                    Path = "/system",
                    Name = "System",
                    Meta = new MenuMetaResponse
                    {
                        Icon = "ion:settings-outline",
                        Title = "system.title",
                        Order = 9997
                    },
                    Children = new List<MenuRouteResponse>
                    {
                        new MenuRouteResponse
                        {
                            Path = "/system/user",
                            Name = "SystemUser",
                            Component = "#/views/system/user/list.vue",
                            Meta = new MenuMetaResponse
                            {
                                Icon = "mdi:account",
                                Title = "system.user.title"
                            }
                        }
                    }
                }
        };
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var userId = _currentUser.Id;
    if (userId is null || !Guid.TryParse(userId, out var _userId))
    {
      await SendUnauthorizedAsync(cancellation: cancellationToken);
      return;
    }

    var query = new GetUserMenuRoutesQuery(_userId);
    var result = await _mediator.Send(query, cancellationToken);

    if (!result.IsSuccess)
    {
      foreach (var error in result.Errors)
      {
        AddError(error);
      }
      await SendErrorsAsync();
      return;
    }

    var menus = result.Value.Select(MapToResponse).ToList();

    await SendAsync(menus);
    return;
  }

  private static MenuRouteResponse MapToResponse(MenuRouteDTO dto)
  {
    var response  = new MenuRouteResponse
    {
      Id = dto.Id,
      Name = dto.Name,
      Path = dto.Path,
      Type = dto.Type,
      Status = dto.Status,
      Pid = dto.Pid,
      Component = dto.Component,
      AuthCode = dto.AuthCode,
      Meta = new MenuMetaResponse
      {
        Icon = dto.Meta.Icon,
        ActiveIcon = dto.Meta.ActiveIcon,
        Title = dto.Meta.Title,
        Order = dto.Meta.Order,
        AffixTab = dto.Meta.AffixTab,
        AffixTabOrder = dto.Meta.AffixTabOrder,
        Badge = dto.Meta.Badge,
        BadgeType = dto.Meta.BadgeType,
        BadgeVariants = dto.Meta.BadgeVariants,
        IframeSrc = dto.Meta.IframeSrc,
        Link = dto.Meta.Link,
        OpenInNewWindow = dto.Meta.OpenInNewWindow,
        KeepAlive = dto.Meta.KeepAlive,
        HideInMenu = dto.Meta.HideInMenu,
        HideInTab = dto.Meta.HideInTab,
        HideInBreadcrumb = dto.Meta.HideInBreadcrumb,
        HideChildrenInMenu = dto.Meta.HideChildrenInMenu,
        Query = dto.Meta.Query,
        ActivePath = dto.Meta.ActivePath,
        MaxNumofOpenTab = dto.Meta.MaxNumofOpenTab,
        NoBasicLayout = dto.Meta.NoBasicLayout,
        Authority = dto.Meta.Authority,
      }
    };

    if (dto.Children != null && dto.Children.Count > 0)
    {
      response.Children = dto.Children.Select(MapToResponse).ToList();
    }

    return response;
  }
}
