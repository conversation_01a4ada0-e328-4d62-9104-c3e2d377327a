﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 角色权限关系表
/// </summary>
public class RolePermission : EntityBase<Guid>
{
  private RolePermission()
  {
    // For EF Core
  }

  public RolePermission(Guid roleId, Guid permissionId)
  {
    RoleId = roleId;
    PermissionId = permissionId;
  }

  public new Guid Id { get; init; } = Guid.NewGuid();

  /// <summary>
  /// 角色ID
  /// </summary>
  public Guid RoleId { get; init; }

  /// <summary>
  /// 权限ID
  /// </summary>
  public Guid PermissionId { get; init; }

  /// <summary>
  /// 角色导航属性
  /// </summary>
  public Role? Role { get; set; }

  /// <summary>
  /// 权限导航属性
  /// </summary>
  public Permission? Permission { get; set; }
}
