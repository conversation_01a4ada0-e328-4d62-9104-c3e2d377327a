using Witlab.Platform.UseCases.Platform.Sessions;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 获取用户会话请求
/// </summary>
public class GetUserSessionsRequest
{
  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; set; }
}

/// <summary>
/// 获取用户会话响应
/// </summary>
public class GetUserSessionsResponse
{
  /// <summary>
  /// 会话列表
  /// </summary>
  public List<UserSessionDTO> Sessions { get; set; } = new();
}

/// <summary>
/// 获取用户会话接口
/// </summary>
// [RequirePermission("session:view")]
public class GetUserSessions(IMediator mediator) : Endpoint<GetUserSessionsRequest, GetUserSessionsResponse>
{
  public override void Configure()
  {
    Get("/platform/sessions/user/{userId}");
    Description(x => x.WithTags("Sessions"));
    Summary(s =>
    {
      s.Summary = "获取指定用户的会话列表";
      s.Description = "获取指定用户的所有活跃会话信息";
      s.ExampleRequest = new GetUserSessionsRequest { UserId = Guid.NewGuid() };
    });
  }

  public override async Task HandleAsync(GetUserSessionsRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserSessionsQuery(request.UserId);
    var result = await mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      Response = new GetUserSessionsResponse
      {
        Sessions = result.Value
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
