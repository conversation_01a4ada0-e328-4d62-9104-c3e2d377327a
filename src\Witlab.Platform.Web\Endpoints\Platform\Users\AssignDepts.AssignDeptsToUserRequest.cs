﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

public class AssignDeptsToUserRequest
{
  public const string Route = "/platform/users/{UserId:guid}/depts";
  public static string BuildRoute(Guid userId) => Route.Replace("{UserId:guid}", userId.ToString());

  public Guid UserId { get; set; }

  [Required]
  public List<Guid> DeptIds { get; set; } = [];
}
