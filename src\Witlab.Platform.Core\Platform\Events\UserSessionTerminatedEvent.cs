namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 用户会话终止事件
/// </summary>
public class UserSessionTerminatedEvent : DomainEventBase
{
  /// <summary>
  /// 会话ID
  /// </summary>
  public Guid SessionId { get; }

  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; }

  /// <summary>
  /// 终止原因
  /// </summary>
  public string Reason { get; }

  public UserSessionTerminatedEvent(Guid sessionId, Guid userId, string userName, string reason)
  {
    SessionId = sessionId;
    UserId = userId;
    UserName = userName;
    Reason = reason;
  }
}
