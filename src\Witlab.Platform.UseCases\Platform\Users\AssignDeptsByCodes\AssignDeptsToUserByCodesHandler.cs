﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.AssignDeptsByCodes;

/// <summary>
/// 根据部门编码分配部门给用户命令处理器
/// </summary>
public class AssignDeptsToUserByCodesHandler : ICommandHandler<AssignDeptsToUserByCodesCommand, Result>
{
  private readonly IUserService _userService;

  public AssignDeptsToUserByCodesHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(AssignDeptsToUserByCodesCommand request, CancellationToken cancellationToken)
  {
    return await _userService.AssignDeptsToUserAsync(request.UserId, request.DeptCodes);
  }
}
