using System.Security.Claims;
using Witlab.Platform.UseCases.Platform.Sessions;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 获取当前用户会话响应
/// </summary>
public class GetMySessionsResponse
{
  /// <summary>
  /// 会话列表
  /// </summary>
  public List<UserSessionDTO> Sessions { get; set; } = new();
}

/// <summary>
/// 获取当前用户会话接口
/// </summary>
public class GetMySessions(IMediator mediator) : EndpointWithoutRequest<GetMySessionsResponse>
{
  public override void Configure()
  {
    Get("/auth/my-sessions");
    Description(x => x.WithTags("Auth"));
    Summary(s =>
    {
      s.Summary = "获取当前用户的会话列表";
      s.Description = "获取当前登录用户的所有活跃会话信息";
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    // 获取当前用户ID
    var userIdClaim = HttpContext.User.FindFirst(ClaimTypes.NameIdentifier);
    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
    {
      AddError("无法获取用户信息");
      await SendErrorsAsync();
      return;
    }

    var query = new GetUserSessionsQuery(userId);
    var result = await mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      Response = new GetMySessionsResponse
      {
        Sessions = result.Value
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
