﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

public class ListMenusRequest
{
  public const string Route = "/platform/menus";

  /// <summary>
  /// 跳过的记录数
  /// </summary>
  public int? Skip { get; set; }

  /// <summary>
  /// 获取的记录数
  /// </summary>
  public int? Take { get; set; }

  /// <summary>
  /// 菜单类型值
  /// </summary>
  [Range(0, 2)]
  public int? MenuTypeValue { get; set; }
}
