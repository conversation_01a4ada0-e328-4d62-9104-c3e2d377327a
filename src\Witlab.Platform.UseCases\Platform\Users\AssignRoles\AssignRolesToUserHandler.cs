﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.AssignRoles;

/// <summary>
/// 分配角色给用户命令处理器
/// </summary>
public class AssignRolesToUserHandler : ICommandHandler<AssignRolesToUserCommand, Result>
{
  private readonly IUserService _userService;

  public AssignRolesToUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(AssignRolesToUserCommand request, CancellationToken cancellationToken)
  {
    return await _userService.AssignRolesToUserAsync(request.UserId, request.RoleIds);
  }
}
