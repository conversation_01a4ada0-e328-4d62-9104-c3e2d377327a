﻿namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 创建用户请求验证器
/// </summary>
public class CreateUserValidator : Validator<CreateUserRequest>
{
  public CreateUserValidator()
  {
    //RuleFor(x => x.UserName)
    //    .NotEmpty().WithMessage("用户名不能为空")
    //    .MinimumLength(3).WithMessage("用户名长度不能少于3个字符")
    //    .MaximumLength(50).WithMessage("用户名长度不能超过50个字符");

    //RuleFor(x => x.Password)
    //    .NotEmpty().WithMessage("密码不能为空")
    //    .MinimumLength(6).WithMessage("密码长度不能少于6个字符")
    //    .MaximumLength(100).WithMessage("密码长度不能超过100个字符");

    //RuleFor(x => x.FullName)
    //    .NotEmpty().WithMessage("姓名不能为空")
    //    .MinimumLength(2).WithMessage("姓名长度不能少于2个字符")
    //    .MaximumLength(50).WithMessage("姓名长度不能超过50个字符");

    //RuleFor(x => x.Email)
    //    .EmailAddress().WithMessage("邮箱格式不正确")
    //    .When(x => !string.IsNullOrEmpty(x.Email));
  }
}
