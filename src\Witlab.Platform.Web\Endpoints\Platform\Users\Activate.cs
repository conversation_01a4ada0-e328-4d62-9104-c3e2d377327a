using Witlab.Platform.UseCases.Platform.Users.Activate;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 激活用户
/// </summary>
[RequirePermission("user:activate")]
public class Activate(IMediator _mediator) : Endpoint<ActivateUserRequest>
{
  public override void Configure()
  {
    Post(ActivateUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "激活用户";
      s.Description = "激活指定用户的账号";
    });
  }

  public override async Task HandleAsync(ActivateUserRequest request, CancellationToken cancellationToken)
  {
    var command = new ActivateUserCommand(request.UserId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await Send<PERSON>rrorsAsync(cancellation: cancellationToken);
  }
}
