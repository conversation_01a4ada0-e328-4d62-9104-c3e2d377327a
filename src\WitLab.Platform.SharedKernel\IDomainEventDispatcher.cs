﻿using Microsoft.EntityFrameworkCore;

namespace WitLab.Platform.SharedKernel;

/// <summary>
/// A simple interface for sending domain events. Can use MediatR or any other implementation.
/// </summary>
public interface IDomainEventDispatcher
{
  Task DispatchAndClearEvents(IEnumerable<IHasDomainEvents> entitiesWithEvents);

  Task DispatchAndClearEvents(DbContext dbContext, int deep = 0, CancellationToken cancellationToken = default);
}
