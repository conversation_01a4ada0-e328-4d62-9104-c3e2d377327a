﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Permissions.List;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 列出所有权限
/// </summary>
public class List(IMediator _mediator) : Endpoint<ListPermissionsRequest, PermissionListResponse>
{
  public override void Configure()
  {
    Get(ListPermissionsRequest.Route);
    Description(x => x.AutoTagOverride("Permission"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "列出所有权限";
      s.Description = "获取系统中的所有权限，支持分页和按类型筛选";
    });
  }

  public override async Task HandleAsync(ListPermissionsRequest request, CancellationToken cancellationToken)
  {
    PermissionType? type = null;
    if (request.TypeValue.HasValue)
    {
      type = PermissionType.FromValue(request.TypeValue.Value);
    }

    var query = new ListPermissionsQuery(request.Skip, request.Take, type);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var permissions = result.Value.Select(dto => new PermissionRecord(
          dto.Id,
          dto.Code,
          dto.Name,
          dto.Description,
          dto.TypeValue,
          dto.TypeName,
          dto.IsEnabled,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      )).ToList();

      Response = new PermissionListResponse
      {
        Permissions = permissions
      };
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
