﻿using System.Text;
using System.Text.Json;

namespace Witlab.Platform.Infrastructure.WitLab.Extensions;
public static class WitLabApiResponseConverter
{
  private static readonly JsonSerializerOptions _jsonOptions = new()
  {
    WriteIndented = true,
    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
  };

  public static async Task<WitLabApiResponse> ToWitLabApiResponse(this HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken = default)
  {
    var response = new WitLabApiResponse()
    {
      StatusCode = (int)httpResponseMessage.StatusCode,
      Message = httpResponseMessage.ReasonPhrase
    };

    foreach (var header in httpResponseMessage.Headers)
    {
      response.Headers.Add(new KeyValuePair<string, IEnumerable<string>>(header.Key, header.Value));
    }

    var responseBytes = await httpResponseMessage.Content.ReadAsByteArrayAsync(cancellationToken);
    var charset = httpResponseMessage.Content.Headers.ContentType?.CharSet;
    var encoding = string.IsNullOrEmpty(charset) ? Encoding.UTF8 : Encoding.GetEncoding(charset);
    var content = encoding.GetString(responseBytes);

    try
    {
      var jsonContent = JsonSerializer.Deserialize<WitLabApiResponse>(content, _jsonOptions);

      if (jsonContent != null && jsonContent.Result != null)
      {
        response.Success = jsonContent.Success;
        try
        {
          // 尝试解析JSON
          var jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonContent.Result?.ToString()!, _jsonOptions);

          response.Result = jsonElement;
        }
        catch (Exception)
        {
          response.Result = jsonContent.Result;
        }
      }
      else
      {
        response.Success = httpResponseMessage.IsSuccessStatusCode;
        response.Result = JsonSerializer.Deserialize<JsonElement>(content, _jsonOptions);
      }
    }
    catch (Exception)
    {
      response.Success = httpResponseMessage.IsSuccessStatusCode;
      response.Result = content;
    }

    return response;
  }
}

