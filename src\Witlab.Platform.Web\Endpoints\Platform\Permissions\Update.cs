﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Permissions.Update;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 更新权限
/// </summary>
public class Update(IMediator _mediator) : Endpoint<UpdatePermissionRequest, UpdatePermissionResponse>
{
  public override void Configure()
  {
    Put(UpdatePermissionRequest.Route);
    Description(x => x.AutoTagOverride("Permission"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "更新权限信息";
      s.Description = "更新权限的基本信息，如编码、名称、类型等";
    });
  }

  public override async Task HandleAsync(UpdatePermissionRequest request, CancellationToken cancellationToken)
  {
    var permissionType = PermissionType.FromValue(request.TypeValue);

    var command = new UpdatePermissionCommand(
        request.PermissionId,
        request.Code!,
        request.Name!,
        permissionType,
        request.Description,
        request.MenuId
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new UpdatePermissionResponse(new PermissionRecord(
          dto.Id,
          dto.Code,
          dto.Name,
          dto.Description,
          dto.TypeValue,
          dto.TypeName,
          dto.IsEnabled,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      ));
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
