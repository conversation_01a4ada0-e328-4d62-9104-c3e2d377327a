﻿using Witlab.Platform.UseCases.Platform.Menus.NameExists;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

public class NameExists(IMediator _mediator) : Endpoint<QueryNameExistsRequest, bool>
{
  public override void Configure()
  {
    Get(QueryNameExistsRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "查询菜单名称是否存在";
      s.Description = "根据菜单ID/菜单名称/父级ID查询菜单名称是否存在";
      s.ExampleRequest = new QueryNameExistsRequest { MenuId = string.Empty, MenuName = "系统管理", ParentId = string.Empty };
    });
  }

  public override async Task HandleAsync(QueryNameExistsRequest request, CancellationToken cancellationToken)
  {
    var query = new NameExistsQuery(
      request.MenuId != null ? Guid.Parse(request.MenuId) : null,
      request.MenuName,
      request.ParentId != null ? Guid.Parse(request.ParentId) : null);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      Response = result;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
