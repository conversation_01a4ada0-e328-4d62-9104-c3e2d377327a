﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Menus.Delete;

/// <summary>
/// 删除菜单命令处理器
/// </summary>
public class DeleteMenuHandler : ICommandHandler<DeleteMenuCommand, Result>
{
  private readonly IMenuService _menuService;

  public DeleteMenuHandler(IMenuService menuService)
  {
    _menuService = menuService;
  }

  public async Task<Result> Handle(DeleteMenuCommand request, CancellationToken cancellationToken)
  {
    return await _menuService.DeleteMenuAsync(request.MenuId);
  }
}
