﻿namespace Witlab.Platform.UseCases.Platform.Users.List;

/// <summary>
/// 列出用户查询处理器
/// </summary>
public class ListUsersHandler : IQueryHandler<ListUsersQuery, Result<IEnumerable<UserDTO>>>
{
  private readonly IListUsersQueryService _queryService;

  public ListUsersHandler(IListUsersQueryService queryService)
  {
    _queryService = queryService;
  }

  public async Task<Result<IEnumerable<UserDTO>>> Handle(ListUsersQuery request, CancellationToken cancellationToken)
  {
    var users = await _queryService.ListAsync(request.Skip, request.Take);
    return Result.Success(users);
  }
}
