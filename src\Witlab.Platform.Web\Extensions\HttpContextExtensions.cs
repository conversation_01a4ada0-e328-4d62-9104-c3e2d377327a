﻿namespace Witlab.Platform.Web.Extensions;

/// <summary>
/// HttpContext 扩展方法
/// </summary>
public static class HttpContextExtensions
{
  /// <summary>
  /// 设置 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key"><PERSON>ie 键</param>
  /// <param name="value"><PERSON>ie 值</param>
  /// <param name="expires">过期时间（分钟）</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void SetCookie(
      this HttpContext context,
      string key,
      string value,
      int? expires = null,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    CookieHelper.SetCookie(context, key, value, expires, httpOnly, secure, sameSite);
  }

  /// <summary>
  /// 获取 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <returns>Cookie 值</returns>
  public static string? GetCookie(this HttpContext context, string key)
  {
    return CookieHelper.GetCookie(context, key);
  }

  /// <summary>
  /// 删除 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void DeleteCookie(
      this HttpContext context,
      string key,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    CookieHelper.DeleteCookie(context, key, httpOnly, secure, sameSite);
  }

  /// <summary>
  /// 设置认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="token">令牌值</param>
  /// <param name="expires">过期时间（分钟）</param>
  public static void SetAuthTokenCookie(
      this HttpContext context,
      string token,
      int expires = 1440) // 默认24小时
  {
    CookieHelper.SetAuthTokenCookie(context, token, expires);
  }

  /// <summary>
  /// 获取认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  /// <returns>令牌值</returns>
  public static string? GetAuthTokenCookie(this HttpContext context)
  {
    return CookieHelper.GetAuthTokenCookie(context);
  }

  /// <summary>
  /// 清除认证令牌 Cookie
  /// </summary>
  /// <param name="context">HTTP 上下文</param>
  public static void ClearAuthTokenCookie(this HttpContext context)
  {
    CookieHelper.ClearAuthTokenCookie(context);
  }

  /// <summary>
  /// 设置对象到 Cookie
  /// </summary>
  /// <typeparam name="T">对象类型</typeparam>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <param name="value">对象值</param>
  /// <param name="expires">过期时间（分钟）</param>
  /// <param name="httpOnly">是否仅 HTTP 访问</param>
  /// <param name="secure">是否仅 HTTPS 访问</param>
  /// <param name="sameSite">同源策略</param>
  public static void SetObjectCookie<T>(
      this HttpContext context,
      string key,
      T value,
      int? expires = null,
      bool httpOnly = true,
      bool secure = true,
      SameSiteMode sameSite = SameSiteMode.Lax)
  {
    CookieHelper.SetObjectCookie(context, key, value, expires, httpOnly, secure, sameSite);
  }

  /// <summary>
  /// 从 Cookie 获取对象
  /// </summary>
  /// <typeparam name="T">对象类型</typeparam>
  /// <param name="context">HTTP 上下文</param>
  /// <param name="key">Cookie 键</param>
  /// <returns>对象值</returns>
  public static T? GetObjectCookie<T>(this HttpContext context, string key)
  {
    return CookieHelper.GetObjectCookie<T>(context, key);
  }
}
