﻿using Witlab.Platform.UseCases.Platform.Users.List;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 列出所有用户
/// </summary>
[RequirePermission("user:list")]
public class List(IMediator _mediator) : Endpoint<ListUsersRequest, List<UserRecord>>
{
  public override void Configure()
  {
    Get(ListUsersRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
        {
          s.Summary = "列出所有用户";
          s.Description = "获取系统中的所有用户，支持分页";
        });
  }

  public override async Task HandleAsync(ListUsersRequest request, CancellationToken cancellationToken)
  {
    var query = new ListUsersQuery(request.Skip, request.Take);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var users = result.Value.Select(dto => new UserRecord(
          dto.Id,
          dto.UserName,
          dto.FullName,
          dto.Email,
          dto.Phone,
          dto.Address,
          dto.Icon,
          dto.SexValue,
          dto.SexName,
          dto.StateValue,
          dto.StateName,
          dto.Remark,
          dto.Roles,
          dto.Depts,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      )).ToList();

      Response = users;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
