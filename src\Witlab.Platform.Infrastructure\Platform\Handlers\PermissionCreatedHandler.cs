﻿using MediatR;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 权限创建事件处理器
/// </summary>
public class PermissionCreatedHandler : INotificationHandler<PermissionCreatedEvent>
{
  private readonly ILogger<PermissionCreatedHandler> _logger;

  public PermissionCreatedHandler(ILogger<PermissionCreatedHandler> logger)
  {
    _logger = logger;
  }

  public Task Handle(PermissionCreatedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理权限创建事件: 权限ID {PermissionId}, 权限编码 {PermissionCode}, 权限名称 {PermissionName}",
        notification.PermissionId, notification.PermissionCode, notification.PermissionName);

    // 这里可以添加权限创建后的业务逻辑
    return Task.CompletedTask;
  }
}
