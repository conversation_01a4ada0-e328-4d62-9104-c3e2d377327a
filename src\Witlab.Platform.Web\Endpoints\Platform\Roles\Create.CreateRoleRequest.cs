﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

public class CreateRoleRequest
{
  public const string Route = "/platform/roles";

  [Required]
  [StringLength(50, MinimumLength = 2)]
  [JsonPropertyName("name")]
  public string? RoleName { get; set; }

  [Required]
  [StringLength(50)]
  [JsonPropertyName("code")]
  public string? RoleCode { get; set; }

  [JsonPropertyName("permissions")]
  public List<string> Permissions { get; set; } = new List<string>();

  [StringLength(500)]
  [JsonPropertyName("remark")]
  public string? Remark { get; set; }

  [JsonPropertyName("status")]
  public int Status { get; set; }
}
