﻿using Witlab.Platform.UseCases.Platform.Menus.Delete;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 删除菜单
/// </summary>
[RequirePermission("menu:delete")]
public class Delete(IMediator _mediator) : Endpoint<DeleteMenuRequest>
{
  public override void Configure()
  {
    Delete(DeleteMenuRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    AllowAnonymous();
    Summary(s =>
        {
          s.Summary = "删除菜单";
          s.Description = "删除指定ID的菜单";
        });
  }

  public override async Task HandleAsync(DeleteMenuRequest request, CancellationToken cancellationToken)
  {
    var command = new DeleteMenuCommand(request.MenuId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
