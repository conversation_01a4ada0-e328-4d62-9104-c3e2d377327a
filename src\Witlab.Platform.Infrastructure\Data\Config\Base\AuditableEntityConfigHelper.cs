﻿namespace Witlab.Platform.Infrastructure.Data.Config.Base;

/// <summary>
/// 可审计实体配置辅助类
/// </summary>
public static class AuditableEntityConfigHelper
{
  /// <summary>
  /// 配置审计字段
  /// </summary>
  /// <typeparam name="TEntity">实体类型</typeparam>
  /// <typeparam name="TId">主键类型</typeparam>
  /// <param name="builder">实体类型构建器</param>
  public static EntityTypeBuilder<TEntity> ConfigureAuditableProperties<TEntity, TId>(this EntityTypeBuilder<TEntity> builder)
      where TEntity : AuditableEntityBase<TId>
      where TId : struct, IEquatable<TId>
  {
    // 配置创建时间
    builder.Property(e => e.Created)
        .IsRequired();

    // 配置创建人
    builder.Property(e => e.CreatedBy)
        .HasMaxLength(50);

    // 配置最后修改时间
    builder.Property(e => e.LastModified)
        .IsRequired();

    // 配置最后修改人
    builder.Property(e => e.LastModifiedBy)
        .HasMaxLength(50);

    return builder;
  }
}
