﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.Web.Configurations.Auth;

/// <summary>
/// 权限验证处理器
/// </summary>
public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
  private readonly IPermissionService _permissionService;

  public PermissionAuthorizationHandler(IPermissionService permissionService)
  {
    _permissionService = permissionService;
  }

  protected override async Task HandleRequirementAsync(
      AuthorizationHandlerContext context,
      PermissionRequirement requirement)
  {
    if (context.User == null)
    {
      return;
    }

    // 从Claims中获取用户ID
    var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
    {
      return;
    }

    // 验证用户是否拥有指定权限
    var result = await _permissionService.ValidateUserPermissionAsync(userId, requirement.Permission);
    if (result.IsSuccess && result.Value)
    {
      context.Succeed(requirement);
    }
  }
}
