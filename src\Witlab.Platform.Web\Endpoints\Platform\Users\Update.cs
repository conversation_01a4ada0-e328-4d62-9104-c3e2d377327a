﻿using Witlab.Platform.UseCases.Platform.Users.Update;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 更新用户
/// </summary>
public class Update(IMediator _mediator) : Endpoint<UpdateUserRequest, UpdateUserResponse>
{
  public override void Configure()
  {
    Put(UpdateUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "更新用户信息";
      s.Description = "更新用户的基本信息，如姓名、邮箱、电话等";
    });
  }

  public override async Task HandleAsync(UpdateUserRequest request, CancellationToken cancellationToken)
  {
    var command = new UpdateUserCommand(
        request.UserId,
        request.FullName!,
        request.Email,
        request.Phone,
        request.Address,
        request.Icon,
        request.SexValue,
        request.StateValue,
        request.Remark,
        request.Depts,
        request.Roles
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new UpdateUserResponse(new UserRecord(
          dto.Id,
          dto.UserName,
          dto.FullName,
          dto.Email,
          dto.Phone,
          dto.Address,
          dto.Icon,
          dto.SexValue,
          dto.SexName,
          dto.StateValue,
          dto.StateName,
          dto.Remark,
          dto.Roles,
          dto.Depts,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      ));
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
