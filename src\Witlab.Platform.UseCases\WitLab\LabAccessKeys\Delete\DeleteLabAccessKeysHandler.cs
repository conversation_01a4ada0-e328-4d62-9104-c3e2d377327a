﻿using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.UseCases.WitLab.LabAccessKeys.Delete;
public class DeleteLabAccessKeysHandler : ICommandHandler<DeleteLabAccessKeysCommand, Result>
{
  private readonly ILabAccessKeysService _labAccessKeysService;

  public DeleteLabAccessKeysHandler(ILabAccessKeysService labAccessKeysService)
  {
    _labAccessKeysService = labAccessKeysService ?? throw new ArgumentNullException(nameof(labAccessKeysService));
  }

  public async Task<Result> Handle(DeleteLabAccessKeysCommand request, CancellationToken cancellationToken)
  {
    var existingKeysResult = await _labAccessKeysService.GetAccessKeys(
        request.userName, request.deptCode, request.roleCode, cancellationToken);

    if (existingKeysResult.IsNotFound())
    {
      return Result.NotFound($"User: {request.userName} Access keys not found.");
    }

    if (!existingKeysResult.IsSuccess)
    {
      return Result.Error(new ErrorList(existingKeysResult.Errors));

    }

    var existingKeys = existingKeysResult.Value;
    var deleteResult = await _labAccessKeysService.DeleteKeysAsync(existingKeys.Id, cancellationToken);

    if (!deleteResult.IsSuccess)
    {
      return Result.Error(new ErrorList(deleteResult.Errors));
    }

    return Result.Success();
  }
}
