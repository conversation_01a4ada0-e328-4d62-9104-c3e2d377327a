﻿namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 更新菜单请求验证器
/// </summary>
public class UpdateMenuValidator : Validator<UpdateMenuRequest>
{
  public UpdateMenuValidator()
  {
    //RuleFor(x => x.MenuId)
    //    .NotEmpty().WithMessage("菜单ID不能为空");

    //RuleFor(x => x.MenuName)
    //    .NotEmpty().WithMessage("菜单名称不能为空")
    //    .MinimumLength(2).WithMessage("菜单名称长度不能少于2个字符")
    //    .MaximumLength(50).WithMessage("菜单名称长度不能超过50个字符");

    //RuleFor(x => x.MenuTypeValue)
    //    .InclusiveBetween(0, 2).WithMessage("菜单类型值必须在0-2之间");

    //RuleFor(x => x.Icon)
    //    .MaximumLength(100).WithMessage("图标长度不能超过100个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Icon));

    //RuleFor(x => x.Router)
    //    .MaximumLength(200).WithMessage("路由地址长度不能超过200个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Router));

    //RuleFor(x => x.Action)
    //    .MaximumLength(200).WithMessage("组件路径长度不能超过200个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Action));

    //RuleFor(x => x.RouterName)
    //    .MaximumLength(50).WithMessage("路由名称长度不能超过50个字符")
    //    .When(x => !string.IsNullOrEmpty(x.RouterName));

    //RuleFor(x => x.Query)
    //    .MaximumLength(200).WithMessage("查询参数长度不能超过200个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Query));

    //RuleFor(x => x.Remark)
    //    .MaximumLength(500).WithMessage("备注长度不能超过500个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Remark));
  }
}
