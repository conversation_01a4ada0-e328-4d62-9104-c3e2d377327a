﻿namespace WitLab.Platform.SharedKernel;
public abstract class AuditableEntityBase : EntityBase
{
  public DateTimeOffset Created { get; set; }

  public string? CreatedBy { get; set; }

  public DateTimeOffset LastModified { get; set; }

  public string? LastModifiedBy { get; set; }
}

public abstract class AuditableEntityBase<TId> : EntityBase<TId> where TId : struct, IEquatable<TId>
{
  public new TId Id { get; set; } = GetDefaultId<TId>();

  public DateTimeOffset Created { get; set; }

  public string? CreatedBy { get; set; }

  public DateTimeOffset LastModified { get; set; }

  public string? LastModifiedBy { get; set; }

  private static T GetDefaultId<T>() where T : struct, IEquatable<TId>
  {
    if (typeof(T) == typeof(Guid))
    {
      return (T)(object)Guid.NewGuid();
    }
    else
    {
      return default!;
    }
  }
}


