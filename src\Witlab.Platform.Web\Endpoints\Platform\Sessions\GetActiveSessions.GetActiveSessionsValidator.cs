﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 获取活跃会话请求验证器
/// </summary>
public class GetActiveSessionsValidator : Validator<GetActiveSessionsRequest>
{
  public GetActiveSessionsValidator()
  {
    RuleFor(x => x.PageNumber)
      .GreaterThan(0)
      .WithMessage("页码必须大于0");

    RuleFor(x => x.PageSize)
      .GreaterThan(0)
      .LessThanOrEqualTo(100)
      .WithMessage("页大小必须在1-100之间");
  }
}
