﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 重置用户密码请求验证器
/// </summary>
public class ResetPasswordValidator : Validator<ResetPasswordRequest>
{
  public ResetPasswordValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.NewPassword)
        .NotEmpty().WithMessage("新密码不能为空")
        .MinimumLength(6).WithMessage("新密码长度不能少于6个字符")
        .MaximumLength(100).WithMessage("新密码长度不能超过100个字符");
  }
}
