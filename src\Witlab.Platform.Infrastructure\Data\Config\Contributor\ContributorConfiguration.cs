﻿namespace Witlab.Platform.Infrastructure.Data.Config.Contributor;

public class ContributorConfiguration : IEntityTypeConfiguration<Witlab.Platform.Core.ContributorAggregate.Contributor>
{
  public void Configure(EntityTypeBuilder<Witlab.Platform.Core.ContributorAggregate.Contributor> builder)
  {
    builder.Property(p => p.Name)
        .HasMaxLength(DataSchemaConstants.DEFAULT_NAME_LENGTH)
        .IsRequired();

    builder.OwnsOne(builder => builder.PhoneNumber);

    builder.Property(x => x.Status)
      .HasConversion(
          x => x.Value,
          x => Witlab.Platform.Core.ContributorAggregate.ContributorStatus.FromValue(x));
  }
}
