﻿namespace Witlab.Platform.UseCases.Platform.Roles.List;

/// <summary>
/// 列出角色查询处理器
/// </summary>
public class ListRolesHandler : IQueryHandler<ListRolesQuery, Result<IEnumerable<RoleDTO>>>
{
  private readonly IListRolesQueryService _queryService;

  public ListRolesHandler(IListRolesQueryService queryService)
  {
    _queryService = queryService;
  }

  public async Task<Result<IEnumerable<RoleDTO>>> Handle(ListRolesQuery request, CancellationToken cancellationToken)
  {
    var roles = await _queryService.ListAsync(request.Skip, request.Take);
    return Result.Success(roles);
  }
}
