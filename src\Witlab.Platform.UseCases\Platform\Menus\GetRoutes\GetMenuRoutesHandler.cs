﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Menus.GetRoutes;

/// <summary>
/// 获取菜单路由查询处理器
/// </summary>
public class GetMenuRoutesHandler : IQueryHandler<GetMenuRoutesQuery, Result<List<MenuRouteDTO>>>
{
  private readonly IMenuService _menuService;

  public GetMenuRoutesHandler(IMenuService menuService)
  {
    _menuService = menuService;
  }

  public async Task<Result<List<MenuRouteDTO>>> Handle(GetMenuRoutesQuery request, CancellationToken cancellationToken)
  {
    var result = await _menuService.GetMenuTreeAsync();

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var menus = result.Value;
    var menuRoutes = menus.Select(MapToRouteDto).ToList();
    return Result.Success(menuRoutes);
  }

  private static MenuRouteDTO MapToRouteDto(Menu menu)
  {
    // 将MenuType枚举转换为前端期望的字符串类型
    string menuType = menu.MenuType.Name.ToLowerInvariant();

    var routeDto = new MenuRouteDTO
    {
      Id = menu.Id,
      Name = menu.MenuName,
      Path = menu.Router ?? string.Empty,
      Type = menuType,
      Status = menu.State.Value,
      Pid = menu.ParentId,
      Component = menu.Component,
      AuthCode = menu.Permission?.Code,
      Redirect = menu.Redirect,
      Meta = new MenuMetaDTO
      {
        Icon = menu.MenuIcon,
        ActiveIcon = menu.ActiveIcon,
        ActivePath = menu.ActivePath,
        Title = menu.Title,
        Order = menu.OrderNum,
        AffixTab = menu.AffixTab,
        AffixTabOrder = menu.AffixTabOrder,
        Badge = menu.Badge,
        BadgeType = menu.BadgeType,
        BadgeVariants = menu.BadgeVariants,
        IframeSrc = menu.IframeSrc,
        Link = menu.Link,
        MaxNumofOpenTab = menu.MaxNumOfOpenTab,
        NoBasicLayout = menu.NoBasicLayout,
        OpenInNewWindow = menu.OpenInNewWindow,
        KeepAlive = menu.KeepAlive,
        HideInMenu = menu.HideInMenu,
        HideInTab = menu.HideInTab,
        HideInBreadcrumb = menu.HideInBreadcrumb,
        HideChildrenInMenu = menu.HideChildrenInMenu,
        Query = menu.Query,
        Authority = menu.Permission?.Roles?.Select(role => role.RoleCode).ToList(),
      }
    };

    if (menu.Children != null && menu.Children.Any())
    {
      routeDto.Children = menu.Children.Select(MapToRouteDto).ToList();
    }

    return routeDto;
  }
}
