using Witlab.Platform.UseCases.Platform.Users.AssignRoles;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 分配角色给用户
/// </summary>
[RequirePermission("user:assign-roles")]
public class AssignRoles(IMediator _mediator) : Endpoint<AssignRolesToUserRequest>
{
  public override void Configure()
  {
    Post(AssignRolesToUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "分配角色给用户";
      s.Description = "为指定用户分配一组角色";
    });
  }

  public override async Task HandleAsync(AssignRolesToUserRequest request, CancellationToken cancellationToken)
  {
    var command = new AssignRolesToUserCommand(
        request.UserId,
        request.RoleIds
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
