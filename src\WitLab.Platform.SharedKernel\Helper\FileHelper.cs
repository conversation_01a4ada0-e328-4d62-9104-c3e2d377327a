﻿using System.Text;

namespace WitLab.Platform.SharedKernel.Helper;

public class FileHelper : IDisposable
{

  private bool _alreadyDispose = false;



  #region 构造函数
  public FileHelper()
  {
    //
    // TODO: 在此处添加构造函数逻辑
    //
  }
  ~FileHelper()
  {
    Dispose(); ;
  }

  protected virtual void Dispose(bool isDisposing)
  {
    if (_alreadyDispose) return;
    _alreadyDispose = true;
  }
  #endregion

  #region IDisposable 成员

  public void Dispose()
  {
    Dispose(true);
    GC.SuppressFinalize(this);
  }

  #endregion

  #region 取得文件后缀名
  /****************************************
    * 函数名称：GetPostfixStr
    * 功能说明：取得文件后缀名
    * 参     数：filename:文件名称
    * 调用示列：
    *            string filename = "aaa.aspx";        
    *            string s = EC.FileObj.GetPostfixStr(filename);         
   *****************************************/
  /// <summary>
  /// 取后缀名
  /// </summary>
  /// <param name="filename">文件名</param>
  /// <returns>.gif|.html格式</returns>
  public static string GetPostfixStr(string filename)
  {
    var start = filename.LastIndexOf(".");
    var length = filename.Length;
    var postfix = filename.Substring(start, length - start);
    return postfix;
  }
  #endregion

  #region 写文件
  /****************************************
    * 函数名称：WriteFile
    * 功能说明：写文件,会覆盖掉以前的内容
    * 参     数：Path:文件路径,Strings:文本内容
    * 调用示列：
    *            string Path = Server.MapPath("Default2.aspx");       
    *            string Strings = "这是我写的内容啊";
    *            EC.FileObj.WriteFile(Path,Strings);
   *****************************************/
  /// <summary>
  /// 写文件
  /// </summary>
  /// <param name="Path">文件路径</param>
  /// <param name="Strings">文件内容</param>
  public static void WriteFile(string Path, string Strings)
  {
    if (!File.Exists(Path))
    {
      var f = File.Create(Path);
      f.Close();
    }
    var f2 = new StreamWriter(Path, false, Encoding.GetEncoding("gb2312"));
    f2.Write(Strings);
    f2.Close();
    f2.Dispose();
  }

  /// <summary>
  /// 写文件
  /// </summary>
  /// <param name="Path">文件路径</param>
  /// <param name="Strings">文件内容</param>
  /// <param name="encode">编码格式</param>
  public static void WriteFile(string Path, string Strings, Encoding encode)
  {
    if (!File.Exists(Path))
    {
      var f = File.Create(Path);
      f.Close();
    }
    var f2 = new StreamWriter(Path, false, encode);
    f2.Write(Strings);
    f2.Close();
    f2.Dispose();
  }
  #endregion

  #region 读文件
  /****************************************
    * 函数名称：ReadFile
    * 功能说明：读取文本内容
    * 参     数：Path:文件路径
    * 调用示列：
    *            string Path = Server.MapPath("Default2.aspx");       
    *            string s = EC.FileObj.ReadFile(Path);
   *****************************************/
  /// <summary>
  /// 读文件
  /// </summary>
  /// <param name="Path">文件路径</param>
  /// <returns></returns>
  public static string ReadFile(string Path)
  {
    var s = "";
    if (!File.Exists(Path))
      s = "不存在相应的目录";
    else
    {
      var f2 = new StreamReader(Path, Encoding.GetEncoding("gb2312"));
      s = f2.ReadToEnd();
      f2.Close();
      f2.Dispose();
    }

    return s;
  }

  /// <summary>
  /// 读文件
  /// </summary>
  /// <param name="Path">文件路径</param>
  /// <param name="encode">编码格式</param>
  /// <returns></returns>
  public static string ReadFile(string Path, Encoding encode)
  {
    var s = "";
    if (!File.Exists(Path))
      s = "不存在相应的目录";
    else
    {
      var f2 = new StreamReader(Path, encode);
      s = f2.ReadToEnd();
      f2.Close();
      f2.Dispose();
    }

    return s;
  }
  #endregion

  #region 追加文件
  /****************************************
    * 函数名称：FileAdd
    * 功能说明：追加文件内容
    * 参     数：Path:文件路径,strings:内容
    * 调用示列：
    *            string Path = Server.MapPath("Default2.aspx");     
    *            string Strings = "新追加内容";
    *            EC.FileObj.FileAdd(Path, Strings);
   *****************************************/
  /// <summary>
  /// 追加文件
  /// </summary>
  /// <param name="Path">文件路径</param>
  /// <param name="strings">内容</param>
  public static void FileAdd(string Path, string strings)
  {
    var sw = File.AppendText(Path);
    sw.Write(strings);
    sw.Flush();
    sw.Close();
  }
  #endregion

  #region 拷贝文件
  /****************************************
    * 函数名称：FileCoppy
    * 功能说明：拷贝文件
    * 参     数：OrignFile:原始文件,NewFile:新文件路径
    * 调用示列：
    *            string orignFile = Server.MapPath("Default2.aspx");     
    *            string NewFile = Server.MapPath("Default3.aspx");
    *            EC.FileObj.FileCoppy(OrignFile, NewFile);
   *****************************************/
  /// <summary>
  /// 拷贝文件
  /// </summary>
  /// <param name="orignFile">原始文件</param>
  /// <param name="NewFile">新文件路径</param>
  public static void FileCoppy(string orignFile, string NewFile)
  {
    File.Copy(orignFile, NewFile, true);
  }

  #endregion

  #region 删除文件
  /****************************************
    * 函数名称：FileDel
    * 功能说明：删除文件
    * 参     数：Path:文件路径
    * 调用示列：
    *            string Path = Server.MapPath("Default3.aspx");    
    *            EC.FileObj.FileDel(Path);
   *****************************************/
  /// <summary>
  /// 删除文件
  /// </summary>
  /// <param name="Path">路径</param>
  public static void FileDel(string Path)
  {
    File.Delete(Path);
  }
  #endregion

  #region 移动文件
  /****************************************
    * 函数名称：FileMove
    * 功能说明：移动文件
    * 参     数：OrignFile:原始路径,NewFile:新文件路径
    * 调用示列：
    *             string orignFile = Server.MapPath("../说明.txt");    
    *             string NewFile = Server.MapPath("http://www.cnblogs.com/说明.txt");
    *             EC.FileObj.FileMove(OrignFile, NewFile);
   *****************************************/
  /// <summary>
  /// 移动文件
  /// </summary>
  /// <param name="orignFile">原始路径</param>
  /// <param name="NewFile">新路径</param>
  public static void FileMove(string orignFile, string NewFile)
  {
    File.Move(orignFile, NewFile);
  }
  #endregion

  #region 在当前目录下创建目录
  /****************************************
    * 函数名称：FolderCreate
    * 功能说明：在当前目录下创建目录
    * 参     数：OrignFolder:当前目录,NewFloder:新目录
    * 调用示列：
    *            string orignFolder = Server.MapPath("test/");    
    *            string NewFloder = "new";
    *            EC.FileObj.FolderCreate(OrignFolder, NewFloder);
   *****************************************/
  /// <summary>
  /// 在当前目录下创建目录
  /// </summary>
  /// <param name="orignFolder">当前目录</param>
  /// <param name="NewFloder">新目录</param>
  public static void FolderCreate(string orignFolder, string NewFloder)
  {
    Directory.SetCurrentDirectory(orignFolder);
    Directory.CreateDirectory(NewFloder);
  }
  #endregion

  #region 递归删除文件夹目录及文件
  /****************************************
    * 函数名称：DeleteFolder
    * 功能说明：递归删除文件夹目录及文件
    * 参     数：dir:文件夹路径
    * 调用示列：
    *            string dir = Server.MapPath("test/");  
    *            EC.FileObj.DeleteFolder(dir);       
   *****************************************/
  /// <summary>
  /// 递归删除文件夹目录及文件
  /// </summary>
  /// <param name="dir"></param>
  /// <returns></returns>
  public static void DeleteFolder(string dir)
  {
    if (Directory.Exists(dir)) //如果存在这个文件夹删除之
    {
      foreach (var d in Directory.GetFileSystemEntries(dir))
      {
        if (File.Exists(d))
          File.Delete(d); //直接删除其中的文件
        else
          DeleteFolder(d); //递归删除子文件夹
      }
      Directory.Delete(dir); //删除已空文件夹
    }

  }
  #endregion

  #region 将指定文件夹下面的所有内容copy到目标文件夹下面 果目标文件夹为只读属性就会报错。
  /****************************************
    * 函数名称：CopyDir
    * 功能说明：将指定文件夹下面的所有内容copy到目标文件夹下面 果目标文件夹为只读属性就会报错。
    * 参     数：srcPath:原始路径,aimPath:目标文件夹
    * 调用示列：
    *            string srcPath = Server.MapPath("test/");  
    *            string aimPath = Server.MapPath("test1/");
    *            EC.FileObj.CopyDir(srcPath,aimPath);   
   *****************************************/
  /// <summary>
  /// 指定文件夹下面的所有内容copy到目标文件夹下面
  /// </summary>
  /// <param name="srcPath">原始路径</param>
  /// <param name="aimPath">目标文件夹</param>
  public static void CopyDir(string srcPath, string aimPath)
  {
    try
    {
      // 检查目标目录是否以目录分割字符结束如果不是则添加之
      if (aimPath[aimPath.Length - 1] != Path.DirectorySeparatorChar)
        aimPath += Path.DirectorySeparatorChar;
      // 判断目标目录是否存在如果不存在则新建之
      if (!Directory.Exists(aimPath))
        Directory.CreateDirectory(aimPath);
      // 得到源目录的文件列表，该里面是包含文件以及目录路径的一个数组
      //如果你指向copy目标文件下面的文件而不包含目录请使用下面的方法
      //string[] fileList = Directory.GetFiles(srcPath);
      var fileList = Directory.GetFileSystemEntries(srcPath);
      //遍历所有的文件和目录
      foreach (var file in fileList)
      {
        //先当作目录处理如果存在这个目录就递归Copy该目录下面的文件

        if (Directory.Exists(file))
          CopyDir(file, aimPath + Path.GetFileName(file));
        //否则直接Copy文件
        else
          File.Copy(file, aimPath + Path.GetFileName(file), true);
      }

    }
    catch (Exception ee)
    {
      throw new Exception(ee.ToString());
    }
  }
  #endregion

  /// <summary>
  /// 获取目录下全部文件名
  /// </summary>
  /// <param name="path"></param>
  /// <param name="pattern"></param>
  /// <returns></returns>
  public static List<string> GetAllFileNames(string path, string pattern = "*")
  {
    var folder = new DirectoryInfo(path).GetFiles(pattern).ToList();

    return folder.Select(x => x.Name).ToList();
  }
  /// <summary>
  /// 文件内容替换
  /// </summary>
  public static string FileContentReplace(string path, string oldStr, string newStr)
  {
    var content = File.ReadAllText(path);

    if (content.Contains(oldStr))
    {
      File.Delete(path);
      File.WriteAllText(path, content.Replace(oldStr, newStr));
    }

    return path;
  }
  /// <summary>
  /// 文件名称
  /// </summary>
  public static string FileNameReplace(string path, string oldStr, string newStr)
  {
    var fileName = Path.GetFileName(path);
    if (!fileName.Contains(oldStr))
    {
      return path;
    }

    var directoryName = Path.GetDirectoryName(path);
    var newFileName = fileName.Replace(oldStr, newStr);
    var newPath = Path.Combine(directoryName ?? "", newFileName);
    File.Move(path, newPath);

    return newPath;
  }
  /// <summary>
  /// 目录名替换
  /// </summary>
  public static string DirectoryNameReplace(string path, string oldStr, string newStr)
  {
    var fileName = Path.GetFileName(path);
    if (!fileName.Contains(oldStr))
    {
      return path;
    }

    var directoryName = Path.GetDirectoryName(path);
    var newFileName = fileName.Replace(oldStr, newStr);
    var newPath = Path.Combine(directoryName ?? "", newFileName);
    Directory.Move(path, newPath);
    return newPath;
  }

  /// <summary>
  ///  全部信息递归替换
  /// </summary>
  /// <param name="dirPath"></param>
  /// <param name="oldStr"></param>
  /// <param name="newStr"></param>
  public static void AllInfoReplace(string dirPath, string oldStr, string newStr)
  {
    var path = DirectoryNameReplace(dirPath, oldStr, newStr);
    var dirInfo = new DirectoryInfo(path);
    var files = dirInfo.GetFiles();
    var dirs = dirInfo.GetDirectories();
    if (files.Length > 0)
    {
      foreach (var f in files)
      {
        FileContentReplace(f.FullName, oldStr, newStr);
        FileNameReplace(f.FullName, oldStr, newStr);
      }
    }
    if (dirs.Length > 0)
    {
      foreach (var d in dirs)
      {
        AllInfoReplace(d.FullName, oldStr, newStr);
      }
    }
  }
}
