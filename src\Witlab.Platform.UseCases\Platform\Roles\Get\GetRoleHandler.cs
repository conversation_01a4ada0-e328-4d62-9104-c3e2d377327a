﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.Get;

/// <summary>
/// 获取角色查询处理器
/// </summary>
public class GetRoleHandler : IQueryHandler<GetRoleQuery, Result<RoleDTO>>
{
  private readonly IRoleService _roleService;

  public GetRoleHandler(IRoleService roleService)
  {
    _roleService = roleService;
  }

  public async Task<Result<RoleDTO>> Handle(GetRoleQuery request, CancellationToken cancellationToken)
  {
    var result = await _roleService.GetRoleAsync(request.RoleId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var role = result.Value;
    return Result.Success(MapToDto(role));
  }

  private static RoleDTO MapToDto(Role role)
  {
    return new RoleDTO(
        role.Id,
        role.RoleName,
        role.RoleCode,
        role.Remark,
        role.State,
        role.Permissions?.Select(p => p.Code).ToArray() ?? Array.Empty<string>(),
        role.Created.DateTime,
        role.CreatedBy,
        role.LastModified.DateTime,
        role.LastModifiedBy
    );
  }
}
