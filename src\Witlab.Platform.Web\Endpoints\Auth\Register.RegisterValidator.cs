﻿using FluentValidation;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 用户注册请求验证器
/// </summary>
public class RegisterValidator : Validator<RegisterRequest>
{
  public RegisterValidator()
  {
    RuleFor(x => x.UserName)
        .NotEmpty().WithMessage("用户名不能为空")
        .MinimumLength(3).WithMessage("用户名长度不能少于3个字符")
        .MaximumLength(50).WithMessage("用户名长度不能超过50个字符")
        .Matches(@"^[a-zA-Z0-9_-]+$").WithMessage("用户名只能包含字母、数字、下划线和连字符");

    RuleFor(x => x.Password)
        .NotEmpty().WithMessage("密码不能为空")
        .MinimumLength(6).WithMessage("密码长度不能少于6个字符")
        .MaximumLength(100).WithMessage("密码长度不能超过100个字符")
        .Matches(@"[A-Z]").WithMessage("密码必须包含至少一个大写字母")
        .Matches(@"[a-z]").WithMessage("密码必须包含至少一个小写字母")
        .Matches(@"[0-9]").WithMessage("密码必须包含至少一个数字")
        .Matches(@"[!@#$%^&*(),.?""':{}|<>]").WithMessage("密码必须包含至少一个特殊字符");

    RuleFor(x => x.FullName)
        .NotEmpty().WithMessage("姓名不能为空")
        .MinimumLength(2).WithMessage("姓名长度不能少于2个字符")
        .MaximumLength(50).WithMessage("姓名长度不能超过50个字符");

    RuleFor(x => x.Email)
        .EmailAddress().WithMessage("邮箱格式不正确")
        .MaximumLength(100).WithMessage("邮箱长度不能超过100个字符")
        .When(x => !string.IsNullOrEmpty(x.Email));
  }
}
