﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.UseCases.Platform.Menus.List;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 列出所有菜单
/// </summary>
[RequirePermission("menu:list")]
public class List(IMediator _mediator) : Endpoint<ListMenusRequest, MenuListResponse>
{
  public override void Configure()
  {
    Get(ListMenusRequest.Route);
    Description(x => x.AutoTagOverride("Menu"));
    Summary(s =>
    {
      s.Summary = "列出所有菜单";
      s.Description = "获取系统中的所有菜单，支持分页和按类型筛选";
    });
  }

  public override async Task HandleAsync(ListMenusRequest request, CancellationToken cancellationToken)
  {
    MenuType? menuType = null;
    if (request.MenuTypeValue.HasValue)
    {
      menuType = MenuType.FromValue(request.MenuTypeValue.Value);
    }

    var query = new ListMenusQuery(request.Skip, request.Take, menuType);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var menus = result.Value.Select(dto => new MenuRecord(
          dto.Id,
          dto.MenuName,
          dto.ParentId,
          dto.MenuTypeValue,
          dto.MenuTypeName,
          dto.OrderNum,
          dto.StateValue,
          dto.StateName,
          dto.MenuIcon,
          dto.ActiveIcon,
          dto.Router,
          dto.RouterName,
          dto.Component,
          dto.Query,
          dto.Remark,
          dto.Title,
          dto.AffixTab,
          dto.AffixTabOrder,
          dto.Badge,
          dto.BadgeType,
          dto.BadgeVariants,
          dto.IframeSrc,
          dto.Link,
          dto.OpenInNewWindow,
          dto.KeepAlive,
          dto.HideInMenu,
          dto.HideInTab,
          dto.HideInBreadcrumb,
          dto.HideChildrenInMenu,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      )).ToList();

      Response = new MenuListResponse
      {
        Menus = menus
      };
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
