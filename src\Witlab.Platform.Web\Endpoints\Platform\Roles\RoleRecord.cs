﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 角色记录
/// </summary>
public class RoleRecord()
{
  public string? Id { get; set; }

  [JsonPropertyName("name")]
  public string? RoleName { get; set; }

  [Required]
  [StringLength(50)]
  [JsonPropertyName("code")]
  public string? RoleCode { get; set; }

  [JsonPropertyName("permissions")]
  public List<string> Permissions { get; set; } = new List<string>();

  [StringLength(500)]
  [JsonPropertyName("remark")]
  public string? Remark { get; set; }

  [JsonPropertyName("status")]
  public int Status { get; set; }

  [JsonPropertyName("createTime")]
  public DateTime CreatedOnUtc { get; set; }
  public string? CreatedBy { get; set; }

  [JsonPropertyName("lastModifiedTime")]
  public DateTime? LastModifiedOnUtc { get; set; }
  public string? LastModifiedBy { get; set; }
}


