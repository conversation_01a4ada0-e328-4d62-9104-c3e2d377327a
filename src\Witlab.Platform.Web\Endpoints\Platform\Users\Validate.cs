﻿using Witlab.Platform.UseCases.Platform.Users.Validate;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 验证用户密码
/// </summary>
public class Validate(IMediator _mediator) : Endpoint<ValidateUserRequest, bool>
{
  public override void Configure()
  {
    Post(ValidateUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "验证用户密码";
      s.Description = "验证用户名和密码是否匹配";
      s.ExampleRequest = new ValidateUserRequest { UserName = "admin", Password = "admin123" };
    });
  }

  public override async Task HandleAsync(ValidateUserRequest request, CancellationToken cancellationToken)
  {
    var command = new ValidateUserCommand(
        request.UserName!,
        request.Password!
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      //var dto = result.Value;
      //Response = new UserRecord(
      //    dto.Id,
      //    dto.UserName,
      //    dto.FullName,
      //    dto.Email,
      //    dto.Phone,
      //    dto.Address,
      //    dto.Icon,
      //    dto.SexValue,
      //    dto.SexName,
      //    dto.StateValue,
      //    dto.StateName,
      //    dto.Remark,
      //    dto.Roles,
      //    dto.Depts,
      //    dto.CreatedOnUtc,
      //    dto.CreatedBy,
      //    dto.LastModifiedOnUtc,
      //    dto.LastModifiedBy
      //);
      await SendAsync(true);
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }
    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
