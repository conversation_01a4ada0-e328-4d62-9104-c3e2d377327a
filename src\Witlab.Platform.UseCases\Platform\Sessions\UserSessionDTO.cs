namespace Witlab.Platform.UseCases.Platform.Sessions;

/// <summary>
/// 用户会话数据传输对象
/// </summary>
public record UserSessionDTO(
    Guid Id,
    Guid UserId,
    string UserName,
    string TokenId,
    DateTime LoginTime,
    DateTime LastActivityTime,
    DateTime TokenExpiresAt,
    string IpAddress,
    string UserAgent,
    string DeviceType,
    string OperatingSystem,
    string Browser,
    string? Location,
    string Status,
    string LoginSource,
    bool IsActive
);

/// <summary>
/// 会话统计信息DTO
/// </summary>
public record SessionStatisticsDTO(
    int TotalOnlineUsers,
    int TotalActiveSessions,
    int WebSessions,
    int MobileSessions,
    int DesktopSessions,
    int ApiSessions,
    int TodayNewSessions,
    DateTime StatisticsTime
);
