﻿using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 注销接口
/// </summary>
public class Logout(IAuthService _authService, IJwtService _jwtService) : Endpoint<LogoutRequest>
{
  public override void Configure()
  {
    Post("/auth/logout");
    Description(x => x.WithTags("Auth"));
    AllowAnonymous(); // 允许匿名访问，因为过期的Token无法通过认证
    Summary(s =>
    {
      s.Summary = "用户注销";
      s.Description = "使当前令牌失效，即使令牌已过期也可以调用";
    });
  }

  public override async Task HandleAsync(LogoutRequest request, CancellationToken cancellationToken)
  {
    // 从请求头中获取访问令牌
    var authHeader = HttpContext.Request.Headers.Authorization.ToString();
    var accessToken = authHeader.StartsWith("Bearer ") ? authHeader.Substring(7) : authHeader;

    if (string.IsNullOrEmpty(accessToken))
    {
      AddError("访问令牌不能为空");
      await SendErrorsAsync(cancellation: cancellationToken);
      return;
    }

    // 从访问令牌中获取用户ID（即使Token过期也尝试获取）
    var userId = _jwtService.GetUserIdFromToken(accessToken);
    if (userId == null)
    {
      // 如果无法从Token获取用户ID，仍然尝试撤销RefreshToken
      if (!string.IsNullOrEmpty(request.RefreshToken))
      {
        // 尝试将访问令牌加入黑名单（如果还没过期）
        await _jwtService.AddToBlacklistAsync(accessToken);

        // 尝试撤销刷新令牌
        var refreshTokenService = HttpContext.RequestServices.GetRequiredService<IRefreshTokenService>();
        await refreshTokenService.RevokeRefreshTokenAsync(request.RefreshToken);
      }

      // 即使Token无效，也返回成功，避免泄露信息
      await SendNoContentAsync(cancellationToken);
      return;
    }

    // 注销
    var result = await _authService.LogoutAsync(userId.Value, accessToken, request.RefreshToken!);

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    // 即使注销失败，也返回成功状态，避免泄露系统内部信息
    await SendNoContentAsync(cancellationToken);
  }
}
