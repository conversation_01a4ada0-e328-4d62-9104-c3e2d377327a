﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.UseCases.Platform.Permissions.Update;

/// <summary>
/// 更新权限命令处理器
/// </summary>
public class UpdatePermissionHandler : ICommandHandler<UpdatePermissionCommand, Result<PermissionDTO>>
{
  private readonly IPermissionService _permissionService;
  private readonly IRepository<Menu> _menuRepository;

  public UpdatePermissionHandler(
    IPermissionService permissionService,
    IRepository<Menu> menuRepository)
  {
    _permissionService = permissionService;
    _menuRepository = menuRepository;
  }

  public async Task<Result<PermissionDTO>> Handle(UpdatePermissionCommand request, CancellationToken cancellationToken)
  {
    // 先查找这个权限当前关联的菜单
    var menuSpec = new MenuByPermissionIdSpec(request.PermissionId);
    var existingMenu = await _menuRepository.FirstOrDefaultAsync(menuSpec);

    // 获取可能的新菜单
    Menu? newMenu = null;
    if (request.MenuId.HasValue)
    {
      newMenu = await _menuRepository.GetByIdAsync(request.MenuId.Value);
      if (newMenu == null)
      {
        return Result.Error("指定的菜单不存在");
      }
    }

    // 更新权限信息
    var result = await _permissionService.UpdatePermissionAsync(
        request.PermissionId,
        request.Code,
        request.Name,
        request.Type,
        request.Description,
        null // MenuId参数已不再使用
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var permission = result.Value;

    // 处理菜单关联关系
    if (existingMenu != null && (request.MenuId == null || request.MenuId != existingMenu.Id))
    {
      // 解除当前菜单关联
      existingMenu.PermissionId = null;
      await _menuRepository.UpdateAsync(existingMenu);
    }

    if (newMenu != null && (existingMenu == null || newMenu.Id != existingMenu.Id))
    {
      // 建立新的菜单关联
      newMenu.PermissionId = permission.Id;
      await _menuRepository.UpdateAsync(newMenu);
    }

    return Result.Success(MapToDto(permission));
  }

  private static PermissionDTO MapToDto(Permission permission)
  {
    return new PermissionDTO(
        permission.Id,
        permission.Code,
        permission.Name,
        permission.Description,
        permission.Type.Value,
        permission.Type.Name,
        permission.IsEnabled,
        permission.Created.DateTime,
        permission.CreatedBy,
        permission.LastModified.DateTime,
        permission.LastModifiedBy
    );
  }
}
