﻿using Witlab.Platform.UseCases.Platform.Permissions.Get;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 根据ID获取权限
/// </summary>
public class GetById(IMediator _mediator) : Endpoint<GetPermissionByIdRequest, PermissionRecord>
{
  public override void Configure()
  {
    Get(GetPermissionByIdRequest.Route);
    Description(x => x.AutoTagOverride("Permission"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "根据ID获取权限";
      s.Description = "根据权限ID获取权限详细信息";
    });
  }

  public override async Task HandleAsync(GetPermissionByIdRequest request, CancellationToken cancellationToken)
  {
    var query = new GetPermissionQuery(request.PermissionId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new PermissionRecord(
          dto.Id,
          dto.Code,
          dto.Name,
          dto.Description,
          dto.TypeValue,
          dto.TypeName,
          dto.IsEnabled,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      );
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
