﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.UseCases.Platform.Permissions.Get;

/// <summary>
/// 获取权限查询处理器
/// </summary>
public class GetPermissionHandler : IQueryHandler<GetPermissionQuery, Result<PermissionDTO>>
{
  private readonly IPermissionService _permissionService;
  private readonly IRepository<Menu> _menuRepository;

  public GetPermissionHandler(
    IPermissionService permissionService,
    IRepository<Menu> menuRepository)
  {
    _permissionService = permissionService;
    _menuRepository = menuRepository;
  }

  public async Task<Result<PermissionDTO>> Handle(GetPermissionQuery request, CancellationToken cancellationToken)
  {
    var result = await _permissionService.GetPermissionAsync(request.PermissionId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var permission = result.Value;

    // 查找与此权限关联的菜单
    Guid? relatedMenuId = null;
    var menuSpec = new MenuByPermissionIdSpec(permission.Id);
    var relatedMenu = await _menuRepository.FirstOrDefaultAsync(menuSpec);
    if (relatedMenu != null)
    {
      relatedMenuId = relatedMenu.Id;
    }

    return Result.Success(MapToDto(permission));
  }

  private static PermissionDTO MapToDto(Permission permission)
  {
    return new PermissionDTO(
        permission.Id,
        permission.Code,
        permission.Name,
        permission.Description,
        permission.Type.Value,
        permission.Type.Name,
        permission.IsEnabled,
        permission.Created.DateTime,
        permission.CreatedBy,
        permission.LastModified.DateTime,
        permission.LastModifiedBy
    );
  }
}
