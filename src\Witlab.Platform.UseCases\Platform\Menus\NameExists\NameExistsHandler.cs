﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Menus.NameExists;
public class NameExistsHandler : IQueryHandler<NameExistsQuery, Result<bool>>
{
  private readonly IMenuService _menuService;

  public NameExistsHandler(IMenuService menuService)
  {
    _menuService = menuService;
  }

  public async Task<Result<bool>> Handle(NameExistsQuery request, CancellationToken cancellationToken)
  {
    return await _menuService.CheckNameExists(request.MenuId, request.MenuName, request.ParentId);
  }
}
