﻿namespace Witlab.Platform.Core.Platform;

public class Sex : SmartEnum<Sex>
{
  /// <summary>
  /// 男性
  /// </summary>
  public static readonly Sex Male = new(nameof(Male), 0);

  /// <summary>
  /// 女性
  /// </summary>
  public static readonly Sex Female = new(nameof(Female), 1);

  /// <summary>
  /// 未知
  /// </summary>
  public static readonly Sex Unknown = new(nameof(Unknown), 2);

  protected Sex(string name, int value) : base(name, value) { }
}
