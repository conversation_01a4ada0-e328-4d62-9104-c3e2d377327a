﻿using System.Security.Claims;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 获取当前用户信息接口
/// </summary>
public class Me(IAuthService _authService) : EndpointWithoutRequest<UserInfoResponse>
{
  public override void Configure()
  {
    Get("/auth/me");
    Description(x => x.WithTags("Auth"));
    Summary(s =>
        {
          s.Summary = "获取当前用户信息";
          s.Description = "返回当前登录用户的详细信息";
        });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    // 从Claims中获取用户ID
    var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
    {
      await SendUnauthorizedAsync(cancellationToken);
      return;
    }

    var result = await _authService.GetUserInfoAsync(userId);

    if (result.IsSuccess)
    {
      var userInfo = result.Value;
      Response = new UserInfoResponse
      {
        Id = userInfo.Id,
        UserName = userInfo.UserName,
        FullName = userInfo.FullName,
        Email = userInfo.Email,
        Phone = userInfo.Phone,
        Icon = userInfo.Icon,
        Roles = userInfo.Roles,
        Permissions = userInfo.Permissions
      };
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
