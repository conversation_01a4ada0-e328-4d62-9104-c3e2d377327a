﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

public class UpdateUserRequest
{
  public const string Route = "/platform/users/{UserId:guid}";
  public static string BuildRoute(Guid userId) => Route.Replace("{UserId:guid}", userId.ToString());

  public Guid UserId { get; set; }

  [Required]
  [StringLength(50, MinimumLength = 3)]
  public string? UserName { get; set; }

  [Required]
  [StringLength(100, MinimumLength = 6)]
  public string? Password { get; set; }

  [Required]
  [StringLength(50, MinimumLength = 2)]
  [JsonPropertyName("realName")]
  public string? FullName { get; set; }

  [EmailAddress]
  public string? Email { get; set; }

  public long? Phone { get; set; }

  public string? Address { get; set; }

  public string? Icon { get; set; }

  public int? SexValue { get; set; }

  public int? StateValue { get; set; }

  public string? Remark { get; set; }

  public List<string>? Depts { get; set; }

  public List<string>? Roles { get; set; }
}
