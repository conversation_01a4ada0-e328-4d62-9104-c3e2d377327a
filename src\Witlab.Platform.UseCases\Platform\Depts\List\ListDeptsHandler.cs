﻿namespace Witlab.Platform.UseCases.Platform.Depts.List;

/// <summary>
/// 列出部门查询处理器
/// </summary>
public class ListDeptsHandler : IQueryHandler<ListDeptsQuery, Result<List<DeptDTO>>>
{
  private readonly IListDeptsQueryService _listDeptsQueryService;

  public ListDeptsHandler(IListDeptsQueryService listDeptsQueryService)
  {
    _listDeptsQueryService = listDeptsQueryService;
  }

  public async Task<Result<List<DeptDTO>>> Handle(ListDeptsQuery request, CancellationToken cancellationToken)
  {
    var depts = await _listDeptsQueryService.ListAsync(request.Skip, request.Take);
    return Result.Success(depts.ToList());
  }
}
