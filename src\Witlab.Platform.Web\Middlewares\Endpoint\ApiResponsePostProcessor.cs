﻿using Microsoft.AspNetCore.WebUtilities;
using Witlab.Platform.Web.Middlewares.Endpoint;

public class ApiResponsePostProcessor<TRequest, TResponse> : IPostProcessor<TRequest, TResponse>
{
  public async Task PostProcessAsync(
      IPostProcessorContext<TRequest, TResponse> context,
      CancellationToken ct)
  {
    var httpCtx = context.HttpContext;
    var statusCode = httpCtx.Response.StatusCode;

    // 获取异常或错误对象(可自定义)
    object? error = null;

    // 一般情况下没有异常，某些中间件会在Items或Features里放异常
    if (context.HasExceptionOccurred)
    {
      //|| context.ExceptionDispatchInfo != null || feature?.Error != null || httpCtx.Items.ContainsKey("Error")

      //var feature = httpCtx.Features.Get<IExceptionHandlerFeature>(); 
      //error = context.ExceptionDispatchInfo?.SourceException.Message ?? feature?.Error.Message ?? httpCtx.Items["Error"];

      error = context.ExceptionDispatchInfo?.SourceException.Message;
    }

    var response = new ApiResponse<TResponse>
    {
      Code = statusCode,
      Data = error == null ? context.Response : default,
      Error = error,
      Message = ReasonPhrases.GetReasonPhrase(statusCode)
    };

    var json = System.Text.Json.JsonSerializer.Serialize(response);
    await httpCtx.Response.BodyWriter.WriteAsync(System.Text.Encoding.UTF8.GetBytes(json), ct);
    return;
  }
}
