﻿using Witlab.Platform.UseCases.Platform.Roles.Update;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 更新角色
/// </summary>
public class Update(IMediator _mediator) : Endpoint<UpdateRoleRequest, UpdateRoleResponse>
{
  public override void Configure()
  {
    Put(UpdateRoleRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "更新角色信息";
      s.Description = "更新角色的基本信息，如名称、编码和备注";
    });
  }

  public override async Task HandleAsync(UpdateRoleRequest request, CancellationToken cancellationToken)
  {
    var command = new UpdateRoleCommand(
        request.RoleId,
        request.RoleName!,
        request.RoleCode!,
        request.Permissions,
        request.Remark,
        request.Status == 1 ? true : false
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new UpdateRoleResponse(new RoleRecord()
      {
        Id = dto.Id.ToString(),
        RoleName = dto.RoleName,
        RoleCode = dto.RoleCode,
        Remark = dto.Remark,
        Status = dto.State ? 1 : 0,
        Permissions = dto.Permissions.ToList(),
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      });
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
