﻿namespace Witlab.Platform.Core.WitLab.Specifications;
public class LabAccessKeysByUser : Specification<LabAccessKeys>
{
  public LabAccessKeysByUser(string userName, string deptCode, string? roleCode)
  {
    if (string.IsNullOrWhiteSpace(userName)) throw new ArgumentException("User name cannot be null or empty.", nameof(userName));
    if (string.IsNullOrWhiteSpace(deptCode)) throw new ArgumentException("Department code cannot be null or empty.", nameof(deptCode));
    Query.Where(x => x.UserName == userName && x.DeptCode == deptCode);

    if (!string.IsNullOrWhiteSpace(roleCode))
    {
      Query.Where(x => x.RoleCode == roleCode);
    }
  }
}
