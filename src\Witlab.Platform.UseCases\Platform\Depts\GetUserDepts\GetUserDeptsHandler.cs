﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Depts.GetUserDepts;

/// <summary>
/// 获取用户部门查询处理器
/// </summary>
public class GetUserDeptsHandler : IQueryHandler<GetUserDeptsQuery, Result<List<DeptDTO>>>
{
  private readonly IUserService _userService;

  public GetUserDeptsHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result<List<DeptDTO>>> Handle(GetUserDeptsQuery request, CancellationToken cancellationToken)
  {
    var result = await _userService.GetUserDeptsAsync(request.UserId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var depts = result.Value;
    var deptDtos = depts.Select(MapToDto).ToList();
    return Result.Success(deptDtos);
  }

  private static DeptDTO MapToDto(Dept dept)
  {
    return new DeptDTO(
        dept.Id,
        dept.DeptName,
        dept.DeptCode,
        dept.ParentId,
        dept.Leader,
        dept.OrderNum,
        dept.State.Value,
        dept.State.Name,
        dept.Remark,
        dept.Created.DateTime,
        dept.CreatedBy,
        dept.LastModified.DateTime,
        dept.LastModifiedBy
    );
  }
}
