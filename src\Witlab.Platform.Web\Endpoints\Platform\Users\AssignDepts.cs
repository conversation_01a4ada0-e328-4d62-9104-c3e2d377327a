using Witlab.Platform.UseCases.Platform.Users.AssignDepts;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 分配部门给用户
/// </summary>
[RequirePermission("user:assign-depts")]
public class AssignDepts(IMediator _mediator) : Endpoint<AssignDeptsToUserRequest>
{
  public override void Configure()
  {
    Post(AssignDeptsToUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "分配部门给用户";
      s.Description = "为指定用户分配一组部门";
    });
  }

  public override async Task HandleAsync(AssignDeptsToUserRequest request, CancellationToken cancellationToken)
  {
    var command = new AssignDeptsToUserCommand(
        request.UserId,
        request.DeptIds
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
