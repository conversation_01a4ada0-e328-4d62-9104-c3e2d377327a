﻿namespace Witlab.Platform.UseCases.Platform.Menus.List;

/// <summary>
/// 列出菜单查询处理器
/// </summary>
public class ListMenusHandler : IQueryHandler<ListMenusQuery, Result<List<MenuDTO>>>
{
  private readonly IListMenusQueryService _listMenusQueryService;

  public ListMenusHandler(IListMenusQueryService listMenusQueryService)
  {
    _listMenusQueryService = listMenusQueryService;
  }

  public async Task<Result<List<MenuDTO>>> Handle(ListMenusQuery request, CancellationToken cancellationToken)
  {
    var menus = await _listMenusQueryService.ListAsync(request.Skip, request.Take, request.MenuType);
    return Result.Success(menus.ToList());
  }
}
