﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 分配部门给用户请求验证器
/// </summary>
public class AssignDeptsToUserValidator : Validator<AssignDeptsToUserRequest>
{
  public AssignDeptsToUserValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.DeptIds)
        .NotNull().WithMessage("部门ID列表不能为空");
  }
}
