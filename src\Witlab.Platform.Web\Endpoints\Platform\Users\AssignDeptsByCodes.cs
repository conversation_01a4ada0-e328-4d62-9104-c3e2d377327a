using Witlab.Platform.UseCases.Platform.Users.AssignDeptsByCodes;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据部门编码分配部门给用户
/// </summary>
[RequirePermission("user:assign-depts")]
public class AssignDeptsByCodes(IMediator _mediator) : Endpoint<AssignDeptsToUserByCodesRequest>
{
  public override void Configure()
  {
    Post(AssignDeptsToUserByCodesRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "根据部门编码分配部门给用户";
      s.Description = "为指定用户分配一组部门（使用部门编码）";
    });
  }

  public override async Task HandleAsync(AssignDeptsToUserByCodesRequest request, CancellationToken cancellationToken)
  {
    var command = new AssignDeptsToUserByCodesCommand(
        request.UserId,
        request.DeptCodes
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
