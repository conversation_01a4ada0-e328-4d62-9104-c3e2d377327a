﻿namespace Witlab.Platform.Core.Platform.Specifications;

/// <summary>
/// 获取所有菜单的规约
/// </summary>
public class AllMenusSpec : Specification<Menu>
{
  public AllMenusSpec(bool activeOnly = true)
  {
    if (activeOnly)
    {
      Query.Where(menu => menu.State == MenuState.Activate);
    }

    Query.Include(m => m.Permission).ThenInclude(p => p!.Roles);

    // 按照排序号排序
    Query.OrderBy(menu => menu.OrderNum);
  }
}
