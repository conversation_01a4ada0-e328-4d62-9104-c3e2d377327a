﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Auth.Models;

/// <summary>
/// 刷新令牌请求
/// </summary>
public class RefreshTokenRequest
{
  /// <summary>
  /// 访问令牌
  /// </summary>
  [Required(ErrorMessage = "访问令牌不能为空")]
  public string? AccessToken { get; set; }

  /// <summary>
  /// 刷新令牌
  /// </summary>
  [Required(ErrorMessage = "刷新令牌不能为空")]
  public string? RefreshToken { get; set; }

  /// <summary>
  /// 登录的部门编码
  /// </summary>
  public string? DeptCode { get; set; } = null;

  /// <summary>
  /// 登录的角色编码
  /// </summary>
  public string? RoleCode { get; set; } = null;
}
