﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

public class AssignDeptsToUserByCodesRequest
{
  public const string Route = "/platform/users/{UserId:guid}/depts/by-codes";
  public static string BuildRoute(Guid userId) => Route.Replace("{UserId:guid}", userId.ToString());

  public Guid UserId { get; set; }

  [Required]
  public List<string> DeptCodes { get; set; } = [];
}
