﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 更新角色请求验证器
/// </summary>
public class UpdateRoleValidator : Validator<UpdateRoleRequest>
{
  public UpdateRoleValidator()
  {
    RuleFor(x => x.RoleId)
        .NotEmpty().WithMessage("角色ID不能为空");

    RuleFor(x => x.RoleName)
        .NotEmpty().WithMessage("角色名称不能为空")
        .MinimumLength(2).WithMessage("角色名称长度不能少于2个字符")
        .MaximumLength(50).WithMessage("角色名称长度不能超过50个字符");

    RuleFor(x => x.RoleCode)
        .NotEmpty().WithMessage("角色编码不能为空")
        .MinimumLength(2).WithMessage("角色编码长度不能少于2个字符")
        .MaximumLength(50).WithMessage("角色编码长度不能超过50个字符");

    RuleFor(x => x.Remark)
        .MaximumLength(500).WithMessage("备注长度不能超过500个字符")
        .When(x => !string.IsNullOrEmpty(x.Remark));
  }
}
