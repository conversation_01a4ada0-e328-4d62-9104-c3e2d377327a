﻿using Ardalis.GuardClauses;
using Witlab.Platform.UseCases.Platform.Depts.Update;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 更新部门
/// </summary>
[RequirePermission("dept:update")]
public class Update(IMediator _mediator) : Endpoint<UpdateDeptRequest, UpdateDeptResponse>
{
  public override void Configure()
  {
    Put(UpdateDeptRequest.Route);
    Description(x => x.AutoTagOverride("Dept"));

    AllowAnonymous();
    Summary(s =>
        {
          s.Summary = "更新部门";
          s.Description = "更新部门信息";
        });
  }

  public override async Task HandleAsync(UpdateDeptRequest request, CancellationToken cancellationToken)
  {
    Guard.Against.NullOrEmpty(request.DeptId, nameof(request.DeptId));

    var command = new UpdateDeptCommand(
        request.DeptId.Value,
        request.DeptName!,
        request.DeptCode!,
        request.ParentId,
        request.Leader,
        request.OrderNum,
        request.Remark,
        request.Status == 1 ? true : false
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      //var dto = result.Value;
      //Response = new UpdateDeptResponse(
      //    dto.Id,
      //    dto.DeptName,
      //    dto.DeptCode,
      //    dto.ParentId,
      //    dto.Leader,
      //    dto.OrderNum,
      //    dto.StateValue,
      //    dto.StateName,
      //    dto.Remark
      //);
      await SendOkAsync();
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
