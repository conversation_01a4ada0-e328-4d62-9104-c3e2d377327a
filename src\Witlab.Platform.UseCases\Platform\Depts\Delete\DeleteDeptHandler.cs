﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Depts.Delete;

/// <summary>
/// 删除部门命令处理器
/// </summary>
public class DeleteDeptHandler : ICommandHandler<DeleteDeptCommand, Result>
{
  private readonly IDeptService _deptService;

  public DeleteDeptHandler(IDeptService deptService)
  {
    _deptService = deptService;
  }

  public async Task<Result> Handle(DeleteDeptCommand request, CancellationToken cancellationToken)
  {
    var result = await _deptService.GetDeptAsync(request.DeptId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }
    var dept = result.Value;

    var deleteResult = await _deptService.DeleteDeptAsync(dept.Id);
    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(deleteResult.Errors));
    }

    return Result.Success();
  }
}
