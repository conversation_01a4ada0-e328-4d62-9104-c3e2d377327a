using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Witlab.Platform.UseCases.Platform.Sessions;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 注销其他设备会话请求
/// </summary>
public class LogoutOtherSessionsRequest
{
  /// <summary>
  /// 注销原因
  /// </summary>
  public string? Reason { get; set; } = "用户主动注销其他设备会话";
}

/// <summary>
/// 注销其他设备会话接口
/// </summary>
public class LogoutOtherSessions(IMediator mediator) : Endpoint<LogoutOtherSessionsRequest>
{
  public override void Configure()
  {
    Post("/auth/logout-other-sessions");
    Description(x => x.WithTags("Auth"));
    Summary(s =>
    {
      s.Summary = "注销其他设备的会话";
      s.Description = "注销当前用户在其他设备上的所有会话，保留当前会话";
      s.ExampleRequest = new LogoutOtherSessionsRequest
      {
        Reason = "安全检查"
      };
    });
  }

  public override async Task HandleAsync(LogoutOtherSessionsRequest request, CancellationToken cancellationToken)
  {
    // 获取当前用户ID
    var userIdClaim = HttpContext.User.FindFirst(ClaimTypes.NameIdentifier);
    if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
    {
      AddError("无法获取用户信息");
      await SendErrorsAsync();
      return;
    }

    // 获取当前会话的JTI
    var currentJti = GetCurrentSessionJti();
    if (string.IsNullOrEmpty(currentJti))
    {
      AddError("无法获取当前会话信息");
      await SendErrorsAsync();
      return;
    }

    // 获取用户的所有会话
    var getUserSessionsQuery = new GetUserSessionsQuery(userId);
    var userSessionsResult = await mediator.Send(getUserSessionsQuery, cancellationToken);

    if (!userSessionsResult.IsSuccess)
    {
      foreach (var error in userSessionsResult.Errors)
      {
        AddError(error);
      }
      await SendErrorsAsync();
      return;
    }

    // 找出除当前会话外的其他会话
    var otherSessions = userSessionsResult.Value
      .Where(s => s.TokenId != currentJti && s.IsActive)
      .ToList();

    if (!otherSessions.Any())
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    // 逐个强制下线其他会话
    var logoutTasks = otherSessions.Select(async session =>
    {
      var command = new ForceLogoutCommand(session.Id, request.Reason);
      return await mediator.Send(command, cancellationToken);
    });

    var results = await Task.WhenAll(logoutTasks);
    var failedCount = results.Count(r => !r.IsSuccess);

    if (failedCount > 0)
    {
      AddError($"部分会话注销失败，失败数量: {failedCount}");
      await SendErrorsAsync();
      return;
    }

    await SendNoContentAsync(cancellationToken);
  }

  /// <summary>
  /// 获取当前会话的JTI
  /// </summary>
  /// <returns>JTI</returns>
  private string? GetCurrentSessionJti()
  {
    try
    {
      // 从Authorization头获取JWT token
      var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
      if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
      {
        return null;
      }

      var token = authHeader.Substring("Bearer ".Length).Trim();
      if (string.IsNullOrEmpty(token))
      {
        return null;
      }

      // 解析JWT token获取JTI
      var jwtHandler = new JwtSecurityTokenHandler();
      var jwtToken = jwtHandler.ReadJwtToken(token);
      return jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
    }
    catch
    {
      return null;
    }
  }
}
