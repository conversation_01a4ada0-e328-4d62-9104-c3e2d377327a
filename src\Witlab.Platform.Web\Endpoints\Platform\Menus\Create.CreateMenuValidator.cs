﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 创建菜单请求验证器
/// </summary>
public class CreateMenuValidator : Validator<CreateMenuRequest>
{
  public CreateMenuValidator()
  {
    RuleFor(x => x.Name)
        .NotEmpty().WithMessage("菜单名称不能为空")
        .MinimumLength(2).WithMessage("菜单名称长度不能少于2个字符")
        .MaximumLength(50).WithMessage("菜单名称长度不能超过50个字符");

    //RuleFor(x => x.Type)
    //    .InclusiveBetween(0, 2).WithMessage("菜单类型值必须在0-2之间");

    RuleFor(x => x.Meta.Icon)
        .MaximumLength(100).WithMessage("图标长度不能超过100个字符")
        .When(x => !string.IsNullOrEmpty(x.Meta.Icon));

    RuleFor(x => x.Path)
        .MaximumLength(200).WithMessage("路由地址长度不能超过200个字符")
        .When(x => !string.IsNullOrEmpty(x.Path));

    RuleFor(x => x.Component)
        .MaximumLength(200).WithMessage("组件路径长度不能超过200个字符")
        .When(x => !string.IsNullOrEmpty(x.Component));

    //RuleFor(x => x.RouterName)
    //    .MaximumLength(50).WithMessage("路由名称长度不能超过50个字符")
    //    .When(x => !string.IsNullOrEmpty(x.RouterName));

    //RuleFor(x => x.Meta.Query)
    //    .MaximumLength(200).WithMessage("查询参数长度不能超过200个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Meta.Query));

    //RuleFor(x => x.Remark)
    //    .MaximumLength(500).WithMessage("备注长度不能超过500个字符")
    //    .When(x => !string.IsNullOrEmpty(x.Remark));
  }
}
