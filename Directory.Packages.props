<Project>
  <ItemGroup>
    <PackageVersion Include="Ardalis.GuardClauses" Version="5.0.0" />
    <PackageVersion Include="Ardalis.HttpClientTestExtensions" Version="4.2.0" />
    <PackageVersion Include="Ardalis.ListStartupServices" Version="1.1.4" />
    <PackageVersion Include="Ardalis.Result" Version="10.1.0" />
    <PackageVersion Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageVersion Include="Ardalis.SharedKernel" Version="2.1.1" />
    <PackageVersion Include="Ardalis.SmartEnum" Version="8.2.0" />
    <PackageVersion Include="Ardalis.Specification" Version="9.1.0" />
    <PackageVersion Include="Ardalis.Specification.EntityFrameworkCore" Version="9.1.0" />
    <PackageVersion Include="Azure.Identity" Version="1.14.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="FastEndpoints" Version="6.1.0" />
    <PackageVersion Include="FastEndpoints.ApiExplorer" Version="2.2.0" />
    <PackageVersion Include="FastEndpoints.Swagger" Version="6.1.0" />
    <PackageVersion Include="FastEndpoints.Swagger.Swashbuckle" Version="2.2.0" />
    <PackageVersion Include="MailKit" Version="4.12.1" />
    <PackageVersion Include="MediatR" Version="12.5.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="ReportGenerator" Version="5.4.7" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="SQLite" Version="3.13.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.0" />
    <PackageVersion Include="Aspire.Hosting.AppHost" Version="9.2.1" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.5.0" />
    <PackageVersion Include="Microsoft.Extensions.ServiceDiscovery" Version="9.2.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageVersion Include="Aspire.Hosting.Testing" Version="9.2.1" />
  </ItemGroup>
</Project>