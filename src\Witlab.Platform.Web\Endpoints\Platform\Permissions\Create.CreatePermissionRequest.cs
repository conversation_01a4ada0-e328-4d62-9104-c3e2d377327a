﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

public class CreatePermissionRequest
{
  public const string Route = "/platform/permissions";

  [Required]
  [StringLength(100, MinimumLength = 2)]
  public string? Code { get; set; }

  [Required]
  [StringLength(100, MinimumLength = 2)]
  public string? Name { get; set; }

  [Required]
  [Range(0, 3)]
  public int TypeValue { get; set; }

  [StringLength(500)]
  public string? Description { get; set; }

  public Guid? MenuId { get; set; }
}
