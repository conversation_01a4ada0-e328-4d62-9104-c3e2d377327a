﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Delete;

/// <summary>
/// 删除用户命令处理器
/// </summary>
public class DeleteUserHandler : ICommandHandler<DeleteUserCommand, Result>
{
  private readonly IUserService _userService;

  public DeleteUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
  {
    return await _userService.DeleteUserAsync(request.UserId);
  }
}
