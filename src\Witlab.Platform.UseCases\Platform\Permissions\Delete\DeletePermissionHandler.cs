﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Permissions.Delete;

/// <summary>
/// 删除权限命令处理器
/// </summary>
public class DeletePermissionHandler : ICommandHandler<DeletePermissionCommand, Result>
{
  private readonly IPermissionService _permissionService;

  public DeletePermissionHandler(IPermissionService permissionService)
  {
    _permissionService = permissionService;
  }

  public async Task<Result> Handle(DeletePermissionCommand request, CancellationToken cancellationToken)
  {
    return await _permissionService.DeletePermissionAsync(request.PermissionId);
  }
}
