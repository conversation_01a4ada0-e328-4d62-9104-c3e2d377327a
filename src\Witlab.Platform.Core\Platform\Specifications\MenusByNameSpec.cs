﻿namespace Witlab.Platform.Core.Platform.Specifications;
public class MenusByNameSpec : Specification<Menu>
{
  public MenusByNameSpec(Guid? menuId, string menuName, Guid? parentId)
  {
    if (menuId != null)
    {
      Query.Where(menu => menu.Id != menuId);
    }

    //允许不同父级菜单下菜单名相同
    //if (parentId != null)
    //{
    //  Query.Where(menu => menu.ParentId == parentId);
    //}

    Query.Where(menu => menu.MenuName == menuName);
  }
}
