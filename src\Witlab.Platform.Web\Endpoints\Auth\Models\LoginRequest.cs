﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Auth.Models;

/// <summary>
/// 登录请求
/// </summary>
public class LoginRequest
{
  /// <summary>
  /// 用户名
  /// </summary>
  [Required(ErrorMessage = "用户名不能为空")]
  public string? UserName { get; set; }

  /// <summary>
  /// 密码
  /// </summary>
  [Required(ErrorMessage = "密码不能为空")]
  public string? Password { get; set; }
}
