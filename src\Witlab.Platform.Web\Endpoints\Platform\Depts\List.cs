﻿using Witlab.Platform.UseCases.Platform.Depts.List;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 列出所有部门
/// </summary>
[RequirePermission("dept:list")]
public class List(IMediator _mediator) : Endpoint<ListDeptsRequest, List<DeptRecord>>
{
  public override void Configure()
  {
    Get(ListDeptsRequest.Route);
    Description(x => x.AutoTagOverride("Dept"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "列出所有部门";
      s.Description = "获取系统中的所有部门，支持分页";
    });
  }

  public override async Task HandleAsync(ListDeptsRequest request, CancellationToken cancellationToken)
  {
    var query = new ListDeptsQuery(request.Skip, request.Take);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var depts = result.Value.Select(dto => new DeptRecord()
      {
        DeptId = dto.Id,
        DeptName = dto.DeptName,
        DeptCode = dto.DeptCode,
        ParentId = dto.ParentId,
        Leader = dto.Leader,
        OrderNum = dto.OrderNum,
        Status = dto.StateValue,
        Remark = dto.Remark,
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      }).ToList();

      Response = depts;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
