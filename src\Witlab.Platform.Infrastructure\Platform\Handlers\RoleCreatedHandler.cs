﻿using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 角色创建事件处理器
/// </summary>
public class RoleCreatedHandler : INotificationHandler<RoleCreatedEvent>
{
  private readonly ILogger<RoleCreatedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public RoleCreatedHandler(ILogger<RoleCreatedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(RoleCreatedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理角色创建事件: 角色ID {RoleId}, 角色编码 {RoleCode}, 角色名 {RoleName}", notification.RoleId, notification.RoleCode, notification.RoleName);
    var syncResult = await _witLabSyncService.SyncAddRoleAsync(notification.RoleCode, notification.RoleName);
    if (syncResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, syncResult.Errors));

    _logger.LogInformation("同步至WitLab Server: 角色编码 {RoleCode}, 角色名：{ UserName }", notification.RoleCode, notification.RoleName);
  }
}
