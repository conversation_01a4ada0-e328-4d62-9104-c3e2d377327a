using Witlab.Platform.UseCases.Platform.Users.ChangePassword;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 修改用户密码
/// </summary>
public class ChangePassword(IMediator _mediator) : Endpoint<ChangePasswordRequest>
{
  public override void Configure()
  {
    Post(ChangePasswordRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "修改用户密码";
      s.Description = "修改指定用户的密码，需要提供旧密码和新密码";
      s.ExampleRequest = new ChangePasswordRequest { UserId = Guid.NewGuid(), OldPassword = "oldpassword", NewPassword = "newpassword" };
    });
  }

  public override async Task HandleAsync(ChangePasswordRequest request, CancellationToken cancellationToken)
  {
    var command = new ChangePasswordCommand(
        request.UserId,
        request.OldPassword!,
        request.NewPassword!
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
