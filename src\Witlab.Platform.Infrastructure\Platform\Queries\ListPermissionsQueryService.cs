﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Permissions.List;

namespace Witlab.Platform.Infrastructure.Platform.Queries;

/// <summary>
/// 列出权限查询服务实现
/// </summary>
public class ListPermissionsQueryService : IListPermissionsQueryService
{
  private readonly AppDbContext _dbContext;

  public ListPermissionsQueryService(AppDbContext dbContext)
  {
    _dbContext = dbContext;
  }

  public async Task<IEnumerable<PermissionDTO>> ListAsync(int? skip, int? take, PermissionType? type = null)
  {
    // 创建基础查询，联接Menu表以获取关联关系
    var query = from p in _dbContext.Permissions
                join m in _dbContext.Menus on p.Id equals m.PermissionId into menus
                from menu in menus.DefaultIfEmpty() // LEFT JOIN
                select new { Permission = p, MenuId = menu != null ? menu.Id : (Guid?)null };

    // 应用过滤条件
    if (type != null)
    {
      query = query.Where(x => x.Permission.Type == type);
    }

    // 应用分页
    if (skip.HasValue)
    {
      query = query.Skip(skip.Value);
    }

    if (take.HasValue)
    {
      query = query.Take(take.Value);
    }

    // 执行查询并转换结果
    var results = await query.ToListAsync();

    return results.Select(x => new PermissionDTO(
        x.Permission.Id,
        x.Permission.Code,
        x.Permission.Name,
        x.Permission.Description,
        x.Permission.Type.Value,
        x.Permission.Type.Name,
        x.Permission.IsEnabled,
        x.Permission.Created.DateTime,
        x.Permission.CreatedBy,
        x.Permission.LastModified.DateTime,
        x.Permission.LastModifiedBy
    ));
  }
}
