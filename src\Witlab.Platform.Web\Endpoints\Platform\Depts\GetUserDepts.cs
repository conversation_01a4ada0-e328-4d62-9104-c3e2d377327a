﻿using Witlab.Platform.UseCases.Platform.Depts.GetUserDepts;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 获取用户部门
/// </summary>
public class GetUserDepts(IMediator _mediator) : Endpoint<GetUserDeptsRequest, List<DeptRecord>>
{
  public override void Configure()
  {
    Get(GetUserDeptsRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "获取用户部门";
      s.Description = "获取指定用户的所有部门";
    });
  }

  public override async Task HandleAsync(GetUserDeptsRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserDeptsQuery(request.UserId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var depts = result.Value.Select(dto => new DeptRecord
      {
        DeptId = dto.Id,
        DeptName = dto.DeptName,
        DeptCode = dto.DeptCode,
        ParentId = dto.ParentId,
        Leader = dto.Leader,
        OrderNum = dto.OrderNum,
        Status = dto.StateValue,
        Remark = dto.Remark,
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      }).ToList();

      Response = depts;
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
