﻿using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.UseCases.WitLab.LabAccessKeys.UpdateLabAccessKeys;
public class UpdateLabAccessKeysHandler : ICommandHandler<UpdateLabAccessKeysCommand, Result<LabAccessKeysDTO>>
{
  private readonly ILabAccessKeysService _labAccessKeysService;

  public UpdateLabAccessKeysHandler(ILabAccessKeysService labAccessKeysService)
  {
    _labAccessKeysService = labAccessKeysService ?? throw new ArgumentNullException(nameof(labAccessKeysService));
  }

  public async Task<Result<LabAccessKeysDTO>> Handle(UpdateLabAccessKeysCommand request, CancellationToken cancellationToken)
  {
    var existingKeysResult = await _labAccessKeysService.GetAccessKeys(
        request.userName, request.deptCode, request.roleCode, cancellationToken);

    Core.WitLab.LabAccessKeys? keys = null;

    if (existingKeysResult.IsSuccess)
    {
      var existingKeys = existingKeysResult.Value;
      // 只要 AccessKey 或 SecretKey 有变化就删除，防止过期
      if (existingKeys.AccessKey != request.accessKey || existingKeys.SecretKey != request.secretKey)
      {
        _ = await _labAccessKeysService.DeleteKeysAsync(existingKeys.Id, cancellationToken);
      }
      else
      {
        keys = existingKeys;
      }
    }

    if (keys == null)
    {
      var createResult = await _labAccessKeysService.CreateKeysAsync(
          request.userName, request.accessKey, request.secretKey,
          request.deptCode, request.roleCode, cancellationToken);

      if (createResult.IsError())
        return Result.Error(new ErrorList(createResult.Errors));

      keys = createResult.Value;
    }

    var dto = new LabAccessKeysDTO(
        keys.UserName,
        keys.DeptCode,
        keys.RoleCode,
        keys.AccessKey,
        keys.SecretKey,
        keys.State.Value,
        keys.State.Name);

    return Result.Success(dto);
  }
}
