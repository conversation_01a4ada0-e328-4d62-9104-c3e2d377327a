﻿using Ardalis.Result;

namespace Witlab.Platform.Infrastructure.WitLab.Interfaces;
public interface IWitLabSyncService
{
  Task<Result> SyncAddDeptAsync(string deptCode, string shareDB = "External", string? deptIdentificationCode = null, CancellationToken cancellationToken = default);

  Task<Result> SyncUpdateDeptAsync(string deptCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default);

  Task<Result> SyncDeleteDeptAsync(string deptCode, CancellationToken cancellationToken = default);

  Task<Result> SyncAddRoleAsync(string roleCode, string roleName, CancellationToken cancellationToken = default);

  Task<Result> SyncUpdateRoleAsync(string roleCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default);

  Task<Result> SyncDeleteRoleAsync(string roleCode, CancellationToken cancellationToken = default);

  Task<Result> SyncAddUserInfoAsync(string userName, string fullName, string? email, CancellationToken cancellationToken = default);

  Task<Result> SyncChangeUserPasswordAsync(string userName, string password, CancellationToken cancellationToken = default);

  Task<Result> SyncUserDeptsAsync(string userName, List<string> deptCodes, CancellationToken cancellationToken = default);

  Task<Result> SyncUserRolesAsync(string userName, List<string> roleCodes, CancellationToken cancellationToken = default);

  Task<Result<(string AccessKey, string SecretKey)>> GetUserAccessKeysAsync(string userName, string deptCode, string? roleCode = null, CancellationToken cancellationToken = default);

}
