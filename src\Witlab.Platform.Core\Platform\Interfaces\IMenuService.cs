﻿namespace Witlab.Platform.Core.Platform.Interfaces;

/// <summary>
/// 菜单领域服务接口
/// </summary>
public interface IMenuService
{
  /// <summary>
  /// 创建菜单
  /// </summary>
  /// <param name="menuName">菜单名称</param>
  /// <param name="menuType">菜单类型</param>
  /// <param name="authCode">权限编码，如果提供则创建对应的权限</param>
  /// <param name="parentId">父菜单ID，如果为null则为顶级菜单</param>
  /// <param name="orderNum">排序号</param>
  /// <param name="icon">图标</param>
  /// <param name="router">路由地址</param>
  /// <param name="component">组件路径</param>
  /// <param name="routerName">路由名称</param>
  /// <param name="query">查询参数</param>
  /// <param name="remark">备注</param>
  /// <param name="title">标题</param>
  /// <param name="affixTab">是否固定标签</param>
  /// <returns>创建的菜单</returns>
  Task<Result<Menu>> CreateMenuAsync(
      string menuName,
      MenuType menuType,
      string? authCode = null,
      Guid? parentId = null,
      int orderNum = 0,
      string? icon = null,
      string? router = null,
      string? component = null,
      string? routerName = null,
      string? redirect = null,
      Dictionary<string, string>? query = null,
      string? remark = null,
      string? title = null,
      bool affixTab = false,
      int? affixTabOrder = null,
      string? badge = null,
      string? badgeType = null,
      string? badgeVariants = null,
      string? iframeSrc = null,
      string? link = null,
      bool? openInNewWindow = null,
      bool? keepAlive = null,
      bool? hideInMenu = null,
      bool? hideInTab = null,
      bool? hideInBreadcrumb = null,
      bool? hideChildrenInMenu = null,
      string? activePath = null,
      int? maxNumofOpenTab = null,
      bool? noBasicLayout = null);

  /// <summary>
  /// 更新菜单
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <param name="menuName">菜单名称</param>
  /// <param name="menuType">菜单类型</param>
  /// <param name="parentId">父菜单ID</param>
  /// <param name="orderNum">排序号</param>
  /// <param name="icon">图标</param>
  /// <param name="router">路由地址</param>
  /// <param name="component">组件路径</param>
  /// <param name="routerName">路由名称</param>
  /// <param name="query">查询参数</param>
  /// <param name="remark">备注</param>
  /// <param name="title">标题</param>
  /// <param name="affixTab">是否固定标签</param>
  /// <returns>更新结果</returns>
  Task<Result<Menu>> UpdateMenuAsync(
      Guid menuId,
      string menuName,
      MenuType menuType,
      string? authCode = null,
      Guid? parentId = null,
      int orderNum = 0,
      string? icon = null,
      string? router = null,
      string? component = null,
      string? routerName = null,
      string? redirect = null,
      Dictionary<string, string>? query = null,
      string? remark = null,
      string? title = null,
      bool affixTab = false,
      int? affixTabOrder = null,
      string? badge = null,
      string? badgeType = null,
      string? badgeVariants = null,
      string? iframeSrc = null,
      string? link = null,
      bool? openInNewWindow = null,
      bool? keepAlive = null,
      bool? hideInMenu = null,
      bool? hideInTab = null,
      bool? hideInBreadcrumb = null,
      bool? hideChildrenInMenu = null,
      string? activePath = null,
      int? maxNumofOpenTab = null,
      bool? noBasicLayout = null);

  /// <summary>
  /// 删除菜单
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>删除结果</returns>
  Task<Result> DeleteMenuAsync(Guid menuId);

  /// <summary>
  /// 获取菜单
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>菜单</returns>
  Task<Result<Menu>> GetMenuAsync(Guid menuId);

  /// <summary>
  /// 获取菜单树
  /// </summary>
  /// <param name="parentId">父菜单ID，如果为null则获取所有菜单</param>
  /// <returns>菜单树</returns>
  Task<Result<List<Menu>>> GetMenuTreeAsync(Guid? parentId = null);

  /// <summary>
  /// 获取子菜单
  /// </summary>
  /// <param name="parentId">父菜单ID</param>
  /// <returns>子菜单列表</returns>
  Task<Result<List<Menu>>> GetChildMenuAsync(Guid parentId);

  /// <summary>
  /// 获取用户菜单
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>菜单列表</returns>
  Task<Result<List<Menu>>> GetUserMenusAsync(Guid userId);

  /// <summary>
  /// 激活菜单
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>激活结果</returns>
  Task<Result> ActivateMenuAsync(Guid menuId);

  /// <summary>
  /// 禁用菜单
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>禁用结果</returns>
  Task<Result> DeactivateMenuAsync(Guid menuId);

  /// <summary>
  /// 获取菜单的权限
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>权限列表</returns>
  Task<Result<List<Permission>>> GetMenuPermissionsAsync(Guid menuId);

  /// <summary>
  /// 获取用户菜单路由
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>菜单路由</returns>
  Task<Result<List<Menu>>> GetUserMenuRoutesAsync(Guid userId);

  /// <summary>
  /// 检查菜单名称是否存在（允许不同级别以及不同父菜单下的菜单名称重复）
  /// </summary>
  /// <param name="menuId"></param>
  /// <param name="menuName"></param>
  /// <returns></returns>
  Task<Result<bool>> CheckNameExists(Guid? menuId, string menuName, Guid? parentId);
}
