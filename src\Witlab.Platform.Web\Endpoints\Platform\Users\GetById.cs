﻿using Witlab.Platform.UseCases.Platform.Users.Get;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据ID获取用户
/// </summary>
public class GetById(IMediator _mediator) : Endpoint<GetUserByIdRequest, UserRecord>
{
  public override void Configure()
  {
    Get(GetUserByIdRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "根据ID获取用户";
      s.Description = "根据用户ID获取用户详细信息";
    });
  }

  public override async Task HandleAsync(GetUserByIdRequest request, CancellationToken cancellationToken)
  {
    var query = new GetUserQuery(request.UserId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new UserRecord(
          dto.Id,
          dto.UserName,
          dto.FullName,
          dto.Email,
          dto.Phone,
          dto.Address,
          dto.Icon,
          dto.SexValue,
          dto.SexName,
          dto.StateValue,
          dto.StateName,
          dto.Remark,
          dto.Roles,
          dto.Depts,
          dto.CreatedOnUtc,
          dto.CreatedBy,
          dto.LastModifiedOnUtc,
          dto.LastModifiedBy
      );
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
