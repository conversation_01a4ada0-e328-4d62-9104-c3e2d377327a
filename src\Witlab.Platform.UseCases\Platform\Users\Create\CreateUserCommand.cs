﻿namespace Witlab.Platform.UseCases.Platform.Users.Create;

/// <summary>
/// 创建用户命令
/// </summary>
public record CreateUserCommand(
    string UserName,
    string Password,
    string FullName,
    string? Email,
    long? Phone,
    string? Address,
    string? Icon,
    int? SexValue,
    int? StateValue,
    string? Remark,
    List<string>? Depts,
    List<string>? Roles
) : ICommand<Result<UserDTO>>;
