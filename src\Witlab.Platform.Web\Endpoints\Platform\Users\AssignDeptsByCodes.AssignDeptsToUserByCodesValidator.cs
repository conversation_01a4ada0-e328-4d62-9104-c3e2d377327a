﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据部门编码分配部门给用户请求验证器
/// </summary>
public class AssignDeptsToUserByCodesValidator : Validator<AssignDeptsToUserByCodesRequest>
{
  public AssignDeptsToUserByCodesValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.DeptCodes)
        .NotNull().WithMessage("部门编码列表不能为空");

    RuleForEach(x => x.DeptCodes)
        .NotEmpty().WithMessage("部门编码不能为空")
        .MaximumLength(50).WithMessage("部门编码长度不能超过50个字符");
  }
}
