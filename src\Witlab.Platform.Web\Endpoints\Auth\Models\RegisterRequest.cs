﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Auth.Models;

/// <summary>
/// 用户注册请求
/// </summary>
public class RegisterRequest
{
  /// <summary>
  /// 用户名
  /// </summary>
  [Required]
  [StringLength(50, MinimumLength = 3)]
  public string? UserName { get; set; }

  /// <summary>
  /// 密码
  /// </summary>
  [Required]
  [StringLength(100, MinimumLength = 6)]
  public string? Password { get; set; }

  /// <summary>
  /// 姓名
  /// </summary>
  [Required]
  [StringLength(50, MinimumLength = 2)]
  public string? FullName { get; set; }

  /// <summary>
  /// 邮箱
  /// </summary>
  [EmailAddress]
  [StringLength(100)]
  public string? Email { get; set; }

  /// <summary>
  /// 验证码（如果需要）
  /// </summary>
  public string? VerificationCode { get; set; }
}
