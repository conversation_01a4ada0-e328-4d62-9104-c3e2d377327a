﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class PermissionConfiguration : IEntityTypeConfiguration<Permission>
{
  public void Configure(EntityTypeBuilder<Permission> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<Permission, Guid>();

    builder.Property(p => p.Code)
        .HasMaxLength(100)
        .IsRequired();

    builder.Property(p => p.Name)
        .HasMaxLength(100)
        .IsRequired();

    builder.Property(p => p.Description)
        .HasMaxLength(500);

    // 配置PermissionType SmartEnum转换
    builder.Property(p => p.Type)
        .HasConversion(
            v => v.Value,
            v => PermissionType.FromValue(v));

    // 配置与Menu的关系
    builder.HasOne(p => p.Menu)
        .WithOne(m => m.Permission)
        .HasForeignKey<Menu>(m => m.PermissionId)
        .IsRequired(false)
        .OnDelete(DeleteBehavior.Cascade);

    // 添加唯一索引，确保权限编码唯一
    builder.HasIndex(p => p.Code)
        .IsUnique();

    builder.ToTable("Permissions");
  }
}
