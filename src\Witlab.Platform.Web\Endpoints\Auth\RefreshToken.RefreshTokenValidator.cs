﻿using FluentValidation;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 刷新令牌请求验证器
/// </summary>
public class RefreshTokenValidator : Validator<RefreshTokenRequest>
{
  public RefreshTokenValidator()
  {
    RuleFor(x => x.AccessToken)
        .NotEmpty().WithMessage("访问令牌不能为空");

    RuleFor(x => x.RefreshToken)
        .NotEmpty().WithMessage("刷新令牌不能为空");
  }
}
