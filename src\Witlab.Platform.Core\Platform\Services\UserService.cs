﻿using System.Data;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 用户领域服务实现
/// </summary>
public class UserService : IUserService
{
  private readonly IRepository<User> _userRepository;
  private readonly IRepository<Role> _roleRepository;
  private readonly IRepository<Dept> _deptRepository;
  private readonly IMediator _mediator;
  private readonly ILogger<UserService> _logger;

  public UserService(
      IRepository<User> userRepository,
      IRepository<Role> roleRepository,
      IRepository<Dept> deptRepository,
      IMediator mediator,
      ILogger<UserService> logger)
  {
    _userRepository = userRepository;
    _roleRepository = roleRepository;
    _deptRepository = deptRepository;
    _mediator = mediator;
    _logger = logger;
  }

  /// <inheritdoc />
  public async Task<Result<User>> CreateUserAsync(string userName, string password, string fullName, List<string>? depts, List<string>? roles, string? email = null, long? phone = null, string? address = null, string? icon = null, int? sexValue = 2, int? stateVaule = 1, string? remark = null)
  {
    try
    {
      // 检查用户名是否已存在
      var spec = new UserByNameSpec(userName);
      var existingUser = await _userRepository.FirstOrDefaultAsync(spec);
      if (existingUser != null)
      {
        return Result.Error("用户名已存在");
      }

      // 创建新用户
      var user = new User(userName, password, fullName, email);
      user.Phone = phone;
      user.Address = address;
      user.Icon = icon;
      user.Sex = Sex.FromValue(sexValue!.Value);
      user.State = WitLabAccessKeyParisState.FromValue(stateVaule!.Value);
      user.Remark = remark;
      user.BuildPassword(); // 加密密码

      // 保存用户
      var createdUser = await _userRepository.AddAsync(user);

      if (depts != null)
      {
        await AssignDeptsToUserAsync(createdUser.Id, depts); // 分配部门
      }

      if (roles != null)
      {
        await AssignRolesToUserAsync(createdUser.Id, roles);
      }

      return Result.Success(createdUser);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建用户时发生错误: {Message}", ex.Message);
      return Result.Error($"创建用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<User>> UpdateUserAsync(Guid userId, string fullName, List<string>? depts, List<string>? roles, string? email = null, long? phone = null, string? address = null, string? icon = null, int? sexValue = 2, int? stateVaule = 1, string? remark = null)
  {
    try
    {
      var spec = new UserInfoSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      // 更新用户信息
      user.FullName = Guard.Against.NullOrEmpty(fullName, nameof(fullName));
      user.Email = email;
      user.Phone = phone;
      user.Address = address;
      user.Icon = icon;
      user.Sex = Sex.FromValue(sexValue!.Value);
      user.State = WitLabAccessKeyParisState.FromValue(stateVaule!.Value);
      user.Remark = remark;

      if (depts != null)
      {
        await AssignDeptsToUserAsync(user.Id, depts); // 分配部门
      }

      if (roles != null)
      {
        await AssignRolesToUserAsync(user.Id, roles);
      }

      await _userRepository.UpdateAsync(user);

      return Result.Success(user);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新用户时发生错误: {Message}", ex.Message);
      return Result.Error($"更新用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeleteUserAsync(Guid userId)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      string userName = user.UserName;

      await _userRepository.DeleteAsync(user);

      // 发布用户删除事件
      await _mediator.Publish(new UserDeletedEvent(userId, userName));

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除用户时发生错误: {Message}", ex.Message);
      return Result.Error($"删除用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<User>> GetUserAsync(Guid userId)
  {
    try
    {
      var spec = new UserInfoSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      return Result.Success(user);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<User>> GetUserByNameAsync(string userName)
  {
    try
    {
      var spec = new UserByNameSpec(userName);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      return Result.Success(user);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据用户名获取用户时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<User>> ValidateUserAsync(string userName, string password)
  {
    try
    {
      var spec = new UserByNameSpec(userName);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      if (!user.JudgePassword(password))
      {
        return Result.Error("密码错误");
      }

      return Result.Success(user);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "验证用户密码时发生错误: {Message}", ex.Message);
      return Result.Error($"验证用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      if (!user.JudgePassword(oldPassword))
      {
        return Result.Error("原密码错误");
      }

      user.EncryPassword.Password = newPassword;
      user.BuildPassword();

      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "修改用户密码时发生错误: {Message}", ex.Message);
      return Result.Error($"修改密码失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ResetPasswordAsync(Guid userId, string newPassword)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      user.EncryPassword.Password = newPassword;
      user.BuildPassword();

      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "重置用户密码时发生错误: {Message}", ex.Message);
      return Result.Error($"重置密码失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> AssignRolesToUserAsync(Guid userId, List<Guid> roleIds)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      List<Role> roles = new List<Role>();
      // 添加新的角色关联
      foreach (var roleId in roleIds)
      {
        var role = await _roleRepository.GetByIdAsync(roleId);
        if (role == null)
        {
          return Result.Error($"角色ID {roleId} 不存在");
        }
        roles.Add(role);

        // 发布角色分配事件
        await _mediator.Publish(new RoleAssignedEvent(userId, roleId));
      }

      user.AssignRoles(roles);

      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配角色给用户时发生错误: {Message}", ex.Message);
      return Result.Error($"分配角色失败: {ex.Message}");
    }
  }

  public async Task<Result> AssignRolesToUserAsync(Guid userId, List<string> roleCodes)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      List<Role> roles = new List<Role>();
      // 添加新的角色关联
      foreach (var code in roleCodes)
      {
        var spec = new RoleByCodeSpec(code);
        var role = await _roleRepository.FirstOrDefaultAsync(spec);
        if (role == null)
        {
          return Result.Error($"角色编码 {code} 不存在");
        }

        roles.Add(role);

        // 发布角色分配事件
        await _mediator.Publish(new RoleAssignedEvent(userId, role.Id));
      }

      user.AssignRoles(roles);

      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配角色给用户时发生错误: {Message}", ex.Message);
      return Result.Error($"分配角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Role>>> GetUserRolesAsync(Guid userId)
  {
    try
    {
      var spec = new UserWithRolesSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      var roles = user.UserRoles.Select(l => l.Role).ToList() ?? new List<Role>();
      return Result.Success(roles);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户角色时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ActivateUserAsync(Guid userId)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      user.State = WitLabAccessKeyParisState.Activate;
      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "激活用户时发生错误: {Message}", ex.Message);
      return Result.Error($"激活用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeactivateUserAsync(Guid userId)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      user.State = WitLabAccessKeyParisState.Deactivate;
      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用用户时发生错误: {Message}", ex.Message);
      return Result.Error($"禁用用户失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> AssignDeptsToUserAsync(Guid userId, List<Guid> deptIds)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      List<Dept> depts = new List<Dept>();
      // 添加新的部门关联
      foreach (var deptId in deptIds)
      {
        var dept = await _deptRepository.GetByIdAsync(deptId);
        if (dept == null)
        {
          return Result.Error($"部门ID {deptId} 不存在");
        }

        depts.Add(dept);

        // 发布部门分配事件
        await _mediator.Publish(new DeptAssignedEvent(userId, deptId));
      }

      user.AssignDepts(depts);
      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配部门给用户时发生错误: {Message}", ex.Message);
      return Result.Error($"分配部门失败: {ex.Message}");
    }
  }

  public async Task<Result> AssignDeptsToUserAsync(Guid userId, List<string> deptCodes)
  {
    try
    {
      var user = await _userRepository.GetByIdAsync(userId);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      List<Dept> depts = new List<Dept>();
      // 添加新的部门关联
      foreach (var code in deptCodes)
      {
        var spec = new DeptByCodeSpec(code);
        var dept = await _deptRepository.FirstOrDefaultAsync(spec);
        if (dept == null)
        {
          return Result.Error($"部门编码 {code} 不存在");
        }

        depts.Add(dept);

        // 发布部门分配事件
        await _mediator.Publish(new DeptAssignedEvent(userId, dept.Id));
      }

      user.AssignDepts(depts);
      await _userRepository.UpdateAsync(user);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配部门给用户时发生错误: {Message}", ex.Message);
      return Result.Error($"分配部门失败: {ex.Message}");
    }
  }

  public async Task<Result<List<Dept>>> GetUserDeptsAsync(Guid userId)
  {
    try
    {
      var spec = new UserWithDeptsSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      var depts = user.UserDepts.Select(l => l.Dept).ToList() ?? new List<Dept>();
      return Result.Success(depts);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户角色时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户角色失败: {ex.Message}");
    }
  }
}
