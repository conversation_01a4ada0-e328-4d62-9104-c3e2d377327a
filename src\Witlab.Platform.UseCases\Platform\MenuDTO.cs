﻿namespace Witlab.Platform.UseCases.Platform;

/// <summary>
/// 菜单数据传输对象
/// </summary>
public record MenuDTO(
    Guid Id,
    string MenuName,
    Guid? ParentId,
    int MenuTypeValue,
    string MenuTypeName,
    int OrderNum,
    int StateValue,
    string StateName,
    string? MenuIcon,
    string? ActiveIcon,
    string? Router,
    string? RouterName,
    string? Redirect,
    string? Component,
    Dictionary<string, string>? Query,
    string? Remark,
    string? Title,
    bool AffixTab,
    int? AffixTabOrder,
    string? Badge,
    string? BadgeType,
    string? BadgeVariants,
    string? IframeSrc,
    string? Link,
    bool OpenInNewWindow,
    bool KeepAlive,
    bool HideInMenu,
    bool HideInTab,
    bool HideInBreadcrumb,
    bool HideChildrenInMenu,
    string? ActivePath,
    int? MaxNumofOpenTab,
    bool? NoBasicLayout,
    DateTime CreatedOnUtc,
    string? CreatedBy,
    DateTime? LastModifiedOnUtc,
    string? LastModifiedBy
);
