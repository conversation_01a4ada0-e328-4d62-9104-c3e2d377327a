﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Menus.Update;

/// <summary>
/// 更新菜单命令处理器
/// </summary>
public class UpdateMenuHandler : ICommandHandler<UpdateMenuCommand, Result<MenuDTO>>
{
  private readonly IMenuService _menuService;

  public UpdateMenuHandler(IMenuService menuService)
  {
    _menuService = menuService;
  }

  public async Task<Result<MenuDTO>> Handle(UpdateMenuCommand request, CancellationToken cancellationToken)
  {
    try
    {
      // 获取要更新的菜单
      var menuResult = await _menuService.GetMenuAsync(request.MenuId);
      if (!menuResult.IsSuccess)
      {
        return Result.Error(new ErrorList(menuResult.Errors));
      }

      var menu = menuResult.Value;

      // 检查父菜单是否存在且不是自己
      if (request.ParentId.HasValue && request.ParentId.Value != Guid.Empty)
      {
        if (request.ParentId.Value == request.MenuId)
        {
          return Result.Error("父菜单不能是自己");
        }

        var parentMenuResult = await _menuService.GetMenuAsync(request.ParentId.Value);
        if (!parentMenuResult.IsSuccess)
        {
          return Result.Error("父菜单不存在");
        }
      }

      // 使用MenuService更新菜单
      var result = await _menuService.UpdateMenuAsync(
          menu.Id,
          request.MenuName,
          request.MenuType,
          request.AuthCode,
          request.ParentId,
          request.OrderNum,
          request.Icon,
          request.Router,
          request.Component,
          request.RouterName,
          request.Redirect,
          request.Query,
          request.Remark,
          request.Title,
          request.AffixTab,
          request.AffixTabOrder,
          request.Badge,
          request.BadgeType,
          request.BadgeVariants,
          request.IframeSrc,
          request.Link,
          request.OpenInNewWindow,
          request.KeepAlive,
          request.HideInMenu,
          request.HideInTab,
          request.HideInBreadcrumb,
          request.HideChildrenInMenu,
          request.ActivePath,
          request.MaxNumofOpenTab,
          request.NoBasicLayout
      );

      if (!result.IsSuccess)
      {
        return Result.Error(new ErrorList(result.Errors));
      }

      var updatedMenu = result.Value;
      return Result.Success(MapToDto(updatedMenu));
    }
    catch (Exception ex)
    {
      return Result.Error($"更新菜单失败: {ex.Message}");
    }
  }

  private static MenuDTO MapToDto(Menu menu)
  {
    return new MenuDTO(
        menu.Id,
        menu.MenuName,
        menu.ParentId,
        menu.MenuType.Value,
        menu.MenuType.Name,
        menu.OrderNum,
        menu.State.Value,
        menu.State.Name,
        menu.MenuIcon,
        menu.ActiveIcon,
        menu.Router,
        menu.RouterName,
        menu.Redirect,
        menu.Component,
        menu.Query,
        menu.Remark,
        menu.Title,
        menu.AffixTab,
        menu.AffixTabOrder,
        menu.Badge,
        menu.BadgeType,
        menu.BadgeVariants,
        menu.IframeSrc,
        menu.Link,
        menu.OpenInNewWindow,
        menu.KeepAlive,
        menu.HideInMenu,
        menu.HideInTab,
        menu.HideInBreadcrumb,
        menu.HideChildrenInMenu,
        menu.ActivePath,
        menu.MaxNumOfOpenTab,
        menu.NoBasicLayout,
        menu.Created.DateTime,
        menu.CreatedBy,
        menu.LastModified.DateTime,
        menu.LastModifiedBy
    );
  }
}
