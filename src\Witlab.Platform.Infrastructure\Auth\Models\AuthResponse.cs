﻿namespace Witlab.Platform.Infrastructure.Auth.Models;

/// <summary>
/// 认证响应
/// </summary>
public class AuthResponse
{
  /// <summary>
  /// 访问令牌
  /// </summary>
  public string AccessToken { get; set; } = string.Empty;

  /// <summary>
  /// 刷新令牌
  /// </summary>
  public string RefreshToken { get; set; } = string.Empty;

  /// <summary>
  /// 过期时间（秒）
  /// </summary>
  public int ExpiresIn { get; set; }

  /// <summary>
  /// 令牌类型
  /// </summary>
  public string TokenType { get; set; } = "Bearer";

  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; set; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; set; } = string.Empty;
}
