﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class UserDeptConfiguration : IEntityTypeConfiguration<UserDept>
{
  public void Configure(EntityTypeBuilder<UserDept> builder)
  {
    // 将主键设置为 UserId 和 DeptId 的复合主键
    builder.HasKey(ud => new { ud.UserId, ud.DeptId });

    builder.Property(p => p.UserId)
        .IsRequired();

    builder.Property(p => p.DeptId)
        .IsRequired();

    // 配置与 User 实体的关系
    builder.HasOne(ud => ud.User) // 使用 UserDept.User 导航属性
        .WithMany(u => u.UserDepts) // 使用 User.UserDepts 集合属性
        .HasForeignKey(ud => ud.UserId)
        .OnDelete(DeleteBehavior.Cascade);

    // 配置与 Dept 实体的关系
    builder.HasOne(ud => ud.Dept) // 使用 UserDept.Dept 导航属性
        .WithMany(d => d.UserDepts) // 使用 Dept.UserDepts 集合属性
        .HasForeignKey(ud => ud.DeptId)
        .OnDelete(DeleteBehavior.Cascade);

    // 由于 (UserId, DeptId) 已是复合主键，不再需要单独的唯一索引
    // builder.HasIndex(ud => new { ud.UserId, ud.DeptId })
    // .IsUnique();

    // ToTable 可以在 UserConfiguration 中定义，这里可以移除以避免重复或冲突
    builder.ToTable("UserDepts");
  }
}
