﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Witlab.Platform.Infrastructure.Migrations;

/// <inheritdoc />
public partial class DeleteUsersRoleIdDeptId : Migration
{
  /// <inheritdoc />
  protected override void Up(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.CreateTable(
        name: "Contributors",
        columns: table => new
        {
          Id = table.Column<int>(type: "INTEGER", nullable: false)
                .Annotation("Sqlite:Autoincrement", true),
          Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
          Status = table.Column<int>(type: "INTEGER", nullable: false),
          PhoneNumber_CountryCode = table.Column<string>(type: "TEXT", nullable: true),
          PhoneNumber_Number = table.Column<string>(type: "TEXT", nullable: true),
          PhoneNumber_Extension = table.Column<string>(type: "TEXT", nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Contributors", x => x.Id);
        });

    migrationBuilder.CreateTable(
        name: "Depts",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          OrderNum = table.Column<int>(type: "INTEGER", nullable: false),
          State = table.Column<int>(type: "INTEGER", nullable: false),
          DeptName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          DeptCode = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          Leader = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          ParentId = table.Column<Guid>(type: "TEXT", nullable: true),
          Remark = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Depts", x => x.Id);
          table.ForeignKey(
                    name: "FK_Depts_Depts_ParentId",
                    column: x => x.ParentId,
                    principalTable: "Depts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
        });

    migrationBuilder.CreateTable(
        name: "Permissions",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          Code = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
          Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
          Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          Type = table.Column<int>(type: "INTEGER", nullable: false),
          IsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Permissions", x => x.Id);
        });

    migrationBuilder.CreateTable(
        name: "RefreshTokens",
        columns: table => new
        {
          Id = table.Column<int>(type: "INTEGER", nullable: false)
                .Annotation("Sqlite:Autoincrement", true),
          UserId = table.Column<Guid>(type: "TEXT", nullable: false),
          Token = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
          ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: false),
          CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
          IsUsed = table.Column<bool>(type: "INTEGER", nullable: false),
          IsRevoked = table.Column<bool>(type: "INTEGER", nullable: false)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_RefreshTokens", x => x.Id);
        });

    migrationBuilder.CreateTable(
        name: "Users",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          UserName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          FullName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          Password = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
          Salt = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          Icon = table.Column<string>(type: "TEXT", maxLength: 4000, nullable: true),
          Ip = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          Address = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
          Phone = table.Column<long>(type: "INTEGER", nullable: true),
          Sex = table.Column<int>(type: "INTEGER", nullable: false),
          State = table.Column<int>(type: "INTEGER", nullable: false),
          Remark = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Users", x => x.Id);
        });

    migrationBuilder.CreateTable(
        name: "Menus",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          Component = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          MenuName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          RouterName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          State = table.Column<int>(type: "INTEGER", nullable: false),
          Router = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
          ParentId = table.Column<Guid>(type: "TEXT", nullable: true),
          Redirect = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          MenuType = table.Column<int>(type: "INTEGER", nullable: false),
          ActiveIcon = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          ActivePath = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          AffixTab = table.Column<bool>(type: "INTEGER", nullable: false),
          AffixTabOrder = table.Column<int>(type: "INTEGER", nullable: true),
          Badge = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          BadgeType = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true),
          BadgeVariants = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true),
          HideInMenu = table.Column<bool>(type: "INTEGER", nullable: false),
          HideInTab = table.Column<bool>(type: "INTEGER", nullable: false),
          HideInBreadcrumb = table.Column<bool>(type: "INTEGER", nullable: false),
          HideChildrenInMenu = table.Column<bool>(type: "INTEGER", nullable: false),
          MenuIcon = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          IframeSrc = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          KeepAlive = table.Column<bool>(type: "INTEGER", nullable: false),
          Link = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          MaxNumOfOpenTab = table.Column<int>(type: "INTEGER", nullable: true),
          NoBasicLayout = table.Column<bool>(type: "INTEGER", nullable: true),
          OpenInNewWindow = table.Column<bool>(type: "INTEGER", nullable: false),
          OrderNum = table.Column<int>(type: "INTEGER", nullable: false),
          Query = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
          Title = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          Remark = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          PermissionId = table.Column<Guid>(type: "TEXT", nullable: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Menus", x => x.Id);
          table.ForeignKey(
                    name: "FK_Menus_Menus_ParentId",
                    column: x => x.ParentId,
                    principalTable: "Menus",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
          table.ForeignKey(
                    name: "FK_Menus_Permissions_PermissionId",
                    column: x => x.PermissionId,
                    principalTable: "Permissions",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
        });

    migrationBuilder.CreateTable(
        name: "UserDepts",
        columns: table => new
        {
          UserId = table.Column<Guid>(type: "TEXT", nullable: false),
          DeptId = table.Column<Guid>(type: "TEXT", nullable: false),
          Id = table.Column<Guid>(type: "TEXT", nullable: false)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_UserDepts", x => new { x.UserId, x.DeptId });
          table.ForeignKey(
                    name: "FK_UserDepts_Depts_DeptId",
                    column: x => x.DeptId,
                    principalTable: "Depts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
          table.ForeignKey(
                    name: "FK_UserDepts_Users_UserId",
                    column: x => x.UserId,
                    principalTable: "Users",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
        });

    migrationBuilder.CreateTable(
        name: "Roles",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          RoleName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          RoleCode = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
          Remark = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
          State = table.Column<bool>(type: "INTEGER", nullable: false),
          MenuId = table.Column<Guid>(type: "TEXT", nullable: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Roles", x => x.Id);
          table.ForeignKey(
                    name: "FK_Roles_Menus_MenuId",
                    column: x => x.MenuId,
                    principalTable: "Menus",
                    principalColumn: "Id");
        });

    migrationBuilder.CreateTable(
        name: "RolePermissions",
        columns: table => new
        {
          RoleId = table.Column<Guid>(type: "TEXT", nullable: false),
          PermissionId = table.Column<Guid>(type: "TEXT", nullable: false),
          Id = table.Column<Guid>(type: "TEXT", nullable: false)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_RolePermissions", x => new { x.RoleId, x.PermissionId });
          table.ForeignKey(
                    name: "FK_RolePermissions_Permissions_PermissionId",
                    column: x => x.PermissionId,
                    principalTable: "Permissions",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
          table.ForeignKey(
                    name: "FK_RolePermissions_Roles_RoleId",
                    column: x => x.RoleId,
                    principalTable: "Roles",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
        });

    migrationBuilder.CreateTable(
        name: "UserRoles",
        columns: table => new
        {
          RoleId = table.Column<Guid>(type: "TEXT", nullable: false),
          UserId = table.Column<Guid>(type: "TEXT", nullable: false),
          Id = table.Column<Guid>(type: "TEXT", nullable: false)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_UserRoles", x => new { x.UserId, x.RoleId });
          table.ForeignKey(
                    name: "FK_UserRoles_Roles_RoleId",
                    column: x => x.RoleId,
                    principalTable: "Roles",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
          table.ForeignKey(
                    name: "FK_UserRoles_Users_UserId",
                    column: x => x.UserId,
                    principalTable: "Users",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
        });

    migrationBuilder.CreateIndex(
        name: "IX_Depts_ParentId",
        table: "Depts",
        column: "ParentId");

    migrationBuilder.CreateIndex(
        name: "IX_Menus_ParentId",
        table: "Menus",
        column: "ParentId");

    migrationBuilder.CreateIndex(
        name: "IX_Menus_PermissionId",
        table: "Menus",
        column: "PermissionId",
        unique: true);

    migrationBuilder.CreateIndex(
        name: "IX_Permissions_Code",
        table: "Permissions",
        column: "Code",
        unique: true);

    migrationBuilder.CreateIndex(
        name: "IX_RefreshTokens_Token",
        table: "RefreshTokens",
        column: "Token",
        unique: true);

    migrationBuilder.CreateIndex(
        name: "IX_RefreshTokens_UserId",
        table: "RefreshTokens",
        column: "UserId");

    migrationBuilder.CreateIndex(
        name: "IX_RolePermissions_PermissionId",
        table: "RolePermissions",
        column: "PermissionId");

    migrationBuilder.CreateIndex(
        name: "IX_RolePermissions_RoleId",
        table: "RolePermissions",
        column: "RoleId");

    migrationBuilder.CreateIndex(
        name: "IX_Roles_MenuId",
        table: "Roles",
        column: "MenuId");

    migrationBuilder.CreateIndex(
        name: "IX_UserDepts_DeptId",
        table: "UserDepts",
        column: "DeptId");

    migrationBuilder.CreateIndex(
        name: "IX_UserRoles_RoleId",
        table: "UserRoles",
        column: "RoleId");
  }

  /// <inheritdoc />
  protected override void Down(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.DropTable(
        name: "Contributors");

    migrationBuilder.DropTable(
        name: "RefreshTokens");

    migrationBuilder.DropTable(
        name: "RolePermissions");

    migrationBuilder.DropTable(
        name: "UserDepts");

    migrationBuilder.DropTable(
        name: "UserRoles");

    migrationBuilder.DropTable(
        name: "Depts");

    migrationBuilder.DropTable(
        name: "Roles");

    migrationBuilder.DropTable(
        name: "Users");

    migrationBuilder.DropTable(
        name: "Menus");

    migrationBuilder.DropTable(
        name: "Permissions");
  }
}
