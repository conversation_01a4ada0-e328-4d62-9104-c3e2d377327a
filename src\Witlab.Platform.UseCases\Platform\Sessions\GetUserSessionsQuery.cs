﻿using Ardalis.Result;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Sessions;

/// <summary>
/// 获取用户会话查询
/// </summary>
public record GetUserSessionsQuery(
  Guid UserId
) : IQuery<Result<List<UserSessionDTO>>>;

/// <summary>
/// 获取用户会话查询处理器
/// </summary>
public class GetUserSessionsQueryHandler : IQueryHandler<GetUserSessionsQuery, Result<List<UserSessionDTO>>>
{
  private readonly IUserSessionService _userSessionService;

  public GetUserSessionsQueryHandler(IUserSessionService userSessionService)
  {
    _userSessionService = userSessionService;
  }

  public async Task<Result<List<UserSessionDTO>>> Handle(GetUserSessionsQuery request, CancellationToken cancellationToken)
  {
    var result = await _userSessionService.GetActiveSessionsByUserIdAsync(request.UserId);
    
    if (!result.IsSuccess)
    {
      return Result<List<UserSessionDTO>>.Error(new ErrorList(result.Errors));
    }

    var sessions = result.Value;
    var sessionDTOs = sessions.Select(s => new UserSessionDTO(
      s.Id,
      s.UserId,
      s.UserName,
      s.TokenId,
      s.LoginTime,
      s.LastActivityTime,
      s.TokenExpiresAt,
      s.SessionInfo.IpAddress,
      s.SessionInfo.UserAgent,
      s.SessionInfo.DeviceType,
      s.SessionInfo.OperatingSystem,
      s.SessionInfo.Browser,
      s.SessionInfo.Location,
      s.Status.ToString(),
      s.LoginSource.ToString(),
      s.IsActive
    )).ToList();

    return Result<List<UserSessionDTO>>.Success(sessionDTOs);
  }
}
