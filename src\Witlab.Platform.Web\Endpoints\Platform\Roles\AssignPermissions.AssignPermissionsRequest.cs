﻿using System.ComponentModel.DataAnnotations;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

public class AssignPermissionsRequest
{
  public const string Route = "/platform/roles/{RoleId:guid}/permissions";
  public static string BuildRoute(Guid roleId) => Route.Replace("{RoleId:guid}", roleId.ToString());

  public Guid RoleId { get; set; }

  [Required]
  public List<Guid> PermissionIds { get; set; } = [];
}
