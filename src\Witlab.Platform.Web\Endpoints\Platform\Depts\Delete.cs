﻿using Witlab.Platform.UseCases.Platform.Depts.Delete;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 删除部门
/// </summary>
[RequirePermission("dept:delete")]
public class Delete(IMediator _mediator) : Endpoint<DeleteDeptRequest>
{
  public override void Configure()
  {
    Delete(DeleteDeptRequest.Route);
    Description(x => x.AutoTagOverride("Dept"));
    AllowAnonymous();
    Summary(s =>
        {
          s.Summary = "删除部门";
          s.Description = "删除指定ID的部门";
        });
  }

  public override async Task HandleAsync(DeleteDeptRequest request, CancellationToken cancellationToken)
  {
    var command = new DeleteDeptCommand(request.DeptId);

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
