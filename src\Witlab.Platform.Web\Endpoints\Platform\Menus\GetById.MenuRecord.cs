﻿namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

public record MenuRecord(
    Guid Id,
    string MenuName,
    Guid? ParentId,
    int MenuTypeValue,
    string MenuTypeName,
    int OrderNum,
    int StateValue,
    string StateName,
    string? MenuIcon,
    string? ActiveIcon,
    string? Router,
    string? RouterName,
    string? Component,
    Dictionary<string, string>? Query,
    string? Remark,
    string? Title,
    bool AffixTab,
    int? AffixTabOrder,
    string? Badge,
    string? BadgeType,
    string? BadgeVariants,
    string? IframeSrc,
    string? Link,
    bool OpenInNewWindow,
    bool KeepAlive,
    bool HideInMenu,
    bool HideInTab,
    bool HideInBreadcrumb,
    bool HideChildrenInMenu,
    DateTime CreatedOnUtc,
    string? CreatedBy,
    DateTime? LastModifiedOnUtc,
    string? LastModifiedBy
);
