﻿using Witlab.Platform.Infrastructure.WitLab;
using Witlab.Platform.Infrastructure.WitLab.Extensions;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;
using Witlab.Platform.Infrastructure.WitLab.Services;

namespace Witlab.Platform.Web.Endpoints.WitLab;

public class Proxy(IWitLabProxyService _witlabProxyService) : EndpointWithoutRequest
{
  private static readonly HashSet<string> HopByHopHeaders = new(StringComparer.OrdinalIgnoreCase)
  {
      "Connection", "Keep-Alive", "Proxy-Authenticate", "Proxy-Authorization",
      "TE", "Trailers", "Transfer-Encoding", "Upgrade"
  };

  public override void Configure()
  {
    Verbs(Http.GET, Http.POST, Http.PUT, Http.DELETE);
    Routes(WitLabProxyRequest.Route);
    Description(x => x.AutoTagOverride("WitLab"));
    AllowAnonymous();
    Summary(s =>
    {
      // XML Docs are used by default but are overridden by these properties:
      //s.Summary = "Create a new Contributor.";
      //s.Description = "Create a new Contributor. A valid name is required.";
      //s.ExampleRequest = new WitLabProxyRequest { Name = "Contributor Name" };
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var path = Route<string?>("path", isRequired: true);
    if (string.IsNullOrEmpty(path))
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    var request = await HttpContext.Request.ToWitLabApiRequest(cancellationToken);
    var response = await _witlabProxyService.ForwardAsync(request, path, cancellationToken);
    await HandleResponseAsync(response, cancellationToken);
  }

  private async Task HandleResponseAsync(WitLabApiResponse response, CancellationToken cancellationToken = default)
  {
    // 复制响应头
    foreach (var header in response.Headers)
    {
      if (!HopByHopHeaders.Contains(header.Key))
      {
        HttpContext.Response.Headers[header.Key] = header.Value.ToArray();
      }
    }

    try
    {
      var result = new
      {
        Code = response.StatusCode,
        Data = new
        {
          response.Result,
          response.Success
        },
        response.Message,
        Error = string.Empty
      };
      await SendAsync(result, statusCode: response.StatusCode, cancellationToken);
    }
    catch (Exception ex)
    {
      await SendAsync(new
      {
        Code = response.StatusCode,
        Data = new
        {
          response.Result,
          response.Success
        },
        response.Message,
        Error = ex.Message
      },
      statusCode: StatusCodes.Status500InternalServerError,
      cancellationToken);
    }
  }
}
