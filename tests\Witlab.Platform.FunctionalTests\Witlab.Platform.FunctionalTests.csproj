﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Shouldly" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
    <PackageReference Include="Ardalis.HttpClientTestExtensions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Witlab.Platform.Infrastructure\Witlab.Platform.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\Witlab.Platform.UseCases\Witlab.Platform.UseCases.csproj" />
    <ProjectReference Include="..\..\src\Witlab.Platform.Web\Witlab.Platform.Web.csproj" />
  </ItemGroup>

</Project>
