﻿namespace Witlab.Platform.UseCases.Platform.Permissions.List;

/// <summary>
/// 列出权限查询处理器
/// </summary>
public class ListPermissionsHandler : IQueryHandler<ListPermissionsQuery, Result<IEnumerable<PermissionDTO>>>
{
  private readonly IListPermissionsQueryService _queryService;

  public ListPermissionsHandler(IListPermissionsQueryService queryService)
  {
    _queryService = queryService;
  }

  public async Task<Result<IEnumerable<PermissionDTO>>> Handle(ListPermissionsQuery request, CancellationToken cancellationToken)
  {
    var permissions = await _queryService.ListAsync(request.Skip, request.Take, request.Type);
    return Result.Success(permissions);
  }
}
