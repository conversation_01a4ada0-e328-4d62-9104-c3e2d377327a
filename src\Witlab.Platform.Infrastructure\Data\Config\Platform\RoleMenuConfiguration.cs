﻿﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class RoleMenuConfiguration : IEntityTypeConfiguration<RoleMenu>
{
    public void Configure(EntityTypeBuilder<RoleMenu> builder)
    {
        // 将主键设置为 RoleId 和 MenuId 的复合主键
        builder.HasKey(rm => new { rm.RoleId, rm.MenuId });

        builder.Property(p => p.RoleId)
            .IsRequired();

        builder.Property(p => p.MenuId)
            .IsRequired();

        // 配置与 Role 实体的关系
        builder.HasOne(rm => rm.Role) // 使用 RoleMenu.Role 导航属性
            .WithMany(r => r.RoleMenus) // 使用 Role.RoleMenus 集合属性
            .HasForeignKey(rm => rm.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        // 配置与 Menu 实体的关系
        builder.HasOne(rm => rm.Menu) // 使用 RoleMenu.Menu 导航属性
            .WithMany(m => m.RoleMenus) // 使用 Menu.RoleMenus 集合属性
            .HasForeignKey(rm => rm.MenuId)
            .OnDelete(DeleteBehavior.Cascade);

        // 由于 (RoleId, MenuId) 已是复合主键，不再需要单独的唯一索引
        // builder.HasIndex(rm => new { rm.RoleId, rm.MenuId })
        // .IsUnique();

        // ToTable 可以在 RoleConfiguration 或 MenuConfiguration 中定义，这里移除
        // builder.ToTable("RoleMenus");
    }
}
