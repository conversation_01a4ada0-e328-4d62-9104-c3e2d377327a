﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 列出角色请求验证器
/// </summary>
public class ListRolesValidator : Validator<ListRolesRequest>
{
  public ListRolesValidator()
  {
    RuleFor(x => x.Skip)
        .GreaterThanOrEqualTo(0).WithMessage("Skip必须大于或等于0")
        .When(x => x.Skip.HasValue);

    RuleFor(x => x.Take)
        .GreaterThan(0).WithMessage("Take必须大于0")
        .LessThanOrEqualTo(100).WithMessage("Take不能超过100")
        .When(x => x.Take.HasValue);
  }
}
