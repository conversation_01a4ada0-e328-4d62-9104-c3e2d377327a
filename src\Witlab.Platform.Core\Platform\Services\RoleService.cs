﻿using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 角色领域服务实现
/// </summary>
public class RoleService : IRoleService
{
  private readonly IRepository<Role> _roleRepository;
  private readonly IRepository<Permission> _permissionRepository;
  private readonly IRepository<Menu> _menuRepository;
  private readonly IMediator _mediator;
  private readonly ILogger<RoleService> _logger;

  public RoleService(
      IRepository<Role> roleRepository,
      IRepository<Permission> permissionRepository,
      IRepository<Menu> menuRepository,
      IMediator mediator,
      ILogger<RoleService> logger)
  {
    _roleRepository = roleRepository;
    _permissionRepository = permissionRepository;
    _menuRepository = menuRepository;
    _mediator = mediator;
    _logger = logger;
  }

  /// <inheritdoc />
  public async Task<Result<Role>> CreateRoleAsync(string roleName, string roleCode, List<string> permissions, string? remark, bool status)
  {
    try
    {
      // 检查角色编码是否已存在
      var spec = new RoleByCodeSpec(roleCode);
      var existingRole = await _roleRepository.FirstOrDefaultAsync(spec);
      if (existingRole != null)
      {
        return Result.Error("角色编码已存在");
      }

      // 创建新角色
      var role = new Role(roleName, roleCode)
      {
        Remark = remark,
        State = status
      };

      // 保存角色
      var createdRole = await _roleRepository.AddAsync(role);

      await AssignPermissionsToRoleAsync(createdRole.Id, permissions);

      // 发布角色创建事件
      //await _mediator.Publish(new RoleCreatedEvent(createdRole.Id, createdRole.RoleName));

      return Result.Success(createdRole);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建角色时发生错误: {Message}", ex.Message);
      return Result.Error($"创建角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Role>> UpdateRoleAsync(Guid roleId, string roleName, string roleCode, List<string> permissions, string? remark, bool status)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      // 检查角色编码是否已被其他角色使用
      if (role.RoleCode != roleCode)
      {
        var spec = new RoleByCodeSpec(roleCode);
        var existingRole = await _roleRepository.FirstOrDefaultAsync(spec);
        if (existingRole != null && existingRole.Id != roleId)
        {
          return Result.Error("角色编码已存在");
        }
      }

      // 更新角色信息
      role.RoleName = Guard.Against.NullOrEmpty(roleName, nameof(roleName));
      role.RoleCode = Guard.Against.NullOrEmpty(roleCode, nameof(roleCode));
      role.Remark = remark;

      await _roleRepository.UpdateAsync(role);
      await AssignPermissionsToRoleAsync(role.Id, permissions);

      return Result.Success(role);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新角色时发生错误: {Message}", ex.Message);
      return Result.Error($"更新角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeleteRoleAsync(Guid roleId)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      await _mediator.Publish(new RoleDeletedEvent(role.Id, role.RoleCode));
      await _roleRepository.DeleteAsync(role);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除角色时发生错误: {Message}", ex.Message);
      return Result.Error($"删除角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Role>> GetRoleAsync(Guid roleId)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      return Result.Success(role);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取角色时发生错误: {Message}", ex.Message);
      return Result.Error($"获取角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Role>> GetRoleByCodeAsync(string roleCode)
  {
    try
    {
      var spec = new RoleByCodeSpec(roleCode);
      var role = await _roleRepository.FirstOrDefaultAsync(spec);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      return Result.Success(role);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据角色编码获取角色时发生错误: {Message}", ex.Message);
      return Result.Error($"获取角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> AssignPermissionsToRoleAsync(Guid roleId, List<Guid> permissionIds)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      // 清除现有权限关联
      role.RolePermissions.Clear();

      // 添加新的权限关联
      foreach (var permissionId in permissionIds)
      {
        var permission = await _permissionRepository.GetByIdAsync(permissionId);
        if (permission == null)
        {
          return Result.Error($"权限ID {permissionId} 不存在");
        }

        //role.RolePermissions.Add(new RolePermission(roleId, permissionId)); 
        role.Permissions.Add(permission);

        // 发布权限授予事件
        await _mediator.Publish(new PermissionGrantedEvent(roleId, permissionId, permission.Code));
      }

      await _roleRepository.UpdateAsync(role);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配权限给角色时发生错误: {Message}", ex.Message);
      return Result.Error($"分配权限失败: {ex.Message}");
    }
  }

  public async Task<Result> AssignPermissionsToRoleAsync(Guid roleId, List<string> permissionCodes)
  {
    try
    {
      var roleWithPermissonsByIdSpec = new RoleWithPermissonsByIdSpec(roleId);
      var role = await _roleRepository.FirstOrDefaultAsync(roleWithPermissonsByIdSpec);
      //var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      // 清除现有权限关联
      role.RolePermissions.Clear();
      //await _rolePermissionRepository.DeleteRangeAsync(role.RolePermissions);

      // 添加新的权限关联
      foreach (var permissionCode in permissionCodes)
      {
        var codeSpec = new PermissionsByCodeSpec(permissionCode);
        var permission = await _permissionRepository.FirstOrDefaultAsync(codeSpec);
        if (permission == null)
        {
          return Result.Error($"权限编码 {permissionCode} 不存在");
        }

        role.RolePermissions.Add(new RolePermission(roleId, permission.Id));

        // 发布权限授予事件
        await _mediator.Publish(new PermissionGrantedEvent(roleId, permission.Id, permission.Code));
      }

      // 保存所有权限关联
      //await _rolePermissionRepository.AddRangeAsync(role.RolePermissions);

      await _roleRepository.UpdateAsync(role);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "分配权限给角色时发生错误: {Message}", ex.Message);
      return Result.Error($"分配权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public Task<Result> AssignMenusToRoleAsync(Guid roleId, List<Guid> menuIds)
  {
    throw new NotImplementedException();
    //try
    //{
    //  var role = await _roleRepository.GetByIdAsync(roleId);
    //  if (role == null)
    //  {
    //    return Result.NotFound("角色不存在");
    //  }

    //  // 清除现有菜单关联
    //  role.RoleMenus.Clear();

    //  // 添加新的菜单关联
    //  foreach (var menuId in menuIds)
    //  {
    //    var menu = await _menuRepository.GetByIdAsync(menuId);
    //    if (menu == null)
    //    {
    //      return Result.Error($"菜单ID {menuId} 不存在");
    //    }

    //    role.RoleMenus.Add(new RoleMenu(roleId, menuId));
    //  }

    //  await _roleRepository.UpdateAsync(role);

    //  return Result.Success();
    //}
    //catch (Exception ex)
    //{
    //  _logger.LogError(ex, "分配菜单给角色时发生错误: {Message}", ex.Message);
    //  return Result.Error($"分配菜单失败: {ex.Message}");
    //}
  }

  /// <inheritdoc />
  public async Task<Result<List<Permission>>> GetRolePermissionsAsync(Guid roleId)
  {
    try
    {
      var spec = new RoleWithPermissionsSpec(roleId);
      var role = await _roleRepository.FirstOrDefaultAsync(spec);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      var permissions = role.Permissions?.ToList() ?? new List<Permission>();
      return Result.Success(permissions);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取角色权限时发生错误: {Message}", ex.Message);
      return Result.Error($"获取角色权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Menu>>> GetRoleMenusAsync(Guid roleId)
  {
    try
    {
      var spec = new RoleWithMenusSpec(roleId);
      var role = await _roleRepository.FirstOrDefaultAsync(spec);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      var menus = role.Permissions.Select(q => q.Menu!).ToList() ?? new List<Menu>();
      return Result.Success(menus);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取角色菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"获取角色菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ActivateRoleAsync(Guid roleId)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      role.State = true;
      await _roleRepository.UpdateAsync(role);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "激活角色时发生错误: {Message}", ex.Message);
      return Result.Error($"激活角色失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeactivateRoleAsync(Guid roleId)
  {
    try
    {
      var role = await _roleRepository.GetByIdAsync(roleId);
      if (role == null)
      {
        return Result.NotFound("角色不存在");
      }

      role.State = false;
      await _roleRepository.UpdateAsync(role);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用角色时发生错误: {Message}", ex.Message);
      return Result.Error($"禁用角色失败: {ex.Message}");
    }
  }
}
