﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 菜单表
///</summary>
public partial class Menu : AuditableEntityBase<Guid>, IAggregateRoot
{
  public Menu()
  {
  }

  public Menu(Guid parentId)
  {
    ParentId = parentId;
  }

  /// <summary>
  /// 组件路径
  /// </summary>
  public string? Component { get; set; }

  /// <summary>
  /// 菜单名
  /// </summary>
  public required string MenuName { get; set; }

  /// <summary>
  /// 路由名称
  /// </summary>
  public string? RouterName { get; set; }

  /// <summary>
  /// 状态
  /// </summary>
  public MenuState State { get; set; } = MenuState.Activate;

  /// <summary>
  /// 路由地址
  /// </summary>
  public string? Router { get; set; }

  /// <summary>
  /// 父级菜单ID，顶级菜单为null或Guid.Empty
  /// </summary>
  public Guid? ParentId { get; set; }

  /// <summary>
  /// 重定向
  /// </summary>
  public string? Redirect { get; set; }

  /// <summary>
  /// 菜单类型
  /// </summary>
  public MenuType MenuType { get; set; } = MenuType.Menu;

  //=======================Meta============================

  /// <summary>
  /// 激活时显示的图标
  /// </summary>
  public string? ActiveIcon { get; set; }

  /// <summary>
  /// 作为路由时，需要激活的菜单的Path
  /// </summary>
  public string? ActivePath { get; set; }

  /// <summary>
  /// 是否固定标签
  /// </summary>
  public bool AffixTab { get; set; } = false;

  /// <summary>
  /// 在标签栏固定的顺序
  /// </summary>
  public int? AffixTabOrder { get; set; }

  /// <summary>
  /// 徽标文本
  /// </summary>
  public string? Badge { get; set; }

  /// <summary>
  /// 徽标类型
  /// </summary>
  public string? BadgeType { get; set; }

  /// <summary>
  /// 徽标变体
  /// </summary>
  public string? BadgeVariants { get; set; }

  /// <summary>
  /// 在菜单中隐藏
  /// </summary>
  public bool HideInMenu { get; set; } = false;

  /// <summary>
  /// 在标签栏中隐藏
  /// </summary>
  public bool HideInTab { get; set; } = false;

  /// <summary>
  /// 在面包屑中隐藏
  /// </summary>
  public bool HideInBreadcrumb { get; set; } = false;

  /// <summary>
  /// 在菜单中隐藏下级
  /// </summary>
  public bool HideChildrenInMenu { get; set; } = false;

  /// <summary>
  /// 菜单图标
  /// </summary>
  public string? MenuIcon { get; set; }

  /// <summary>
  /// iframe源地址(用于嵌入式页面)
  /// </summary>
  public string? IframeSrc { get; set; }

  /// <summary>
  /// 是否缓存页面
  /// </summary>
  public bool KeepAlive { get; set; } = false;

  /// <summary>
  /// 外部链接(用于链接类型)
  /// </summary>
  public string? Link { get; set; }

  /// <summary>
  /// 同一个路由最大打开的标签数
  /// </summary>
  public int? MaxNumOfOpenTab { get; set; }

  /// <summary>
  /// 无需基础布局
  /// </summary>
  public bool? NoBasicLayout { get; set; }

  /// <summary>
  /// 是否在新窗口打开
  /// </summary>
  public bool OpenInNewWindow { get; set; } = false;

  /// <summary>
  /// 排序
  /// </summary>
  public int OrderNum { get; set; } = -1;

  /// <summary>
  /// 查询参数
  /// </summary>
  public Dictionary<string,string>? Query { get; set; }

  /// <summary>
  /// Meta标题
  /// </summary>
  public string? Title { get; set; }

  /// <summary>
  /// 备注
  /// </summary>
  public string? Remark { get; set; }

  /// <summary>
  /// 子菜单
  /// </summary>
  public List<Menu>? Children { get; set; }

  public Guid? PermissionId { get; set; }
  /// <summary>
  /// 关联的权限
  /// </summary>
  public Permission? Permission { get; set; }

  /// <summary>
  /// 角色集合
  /// </summary>
  public ICollection<Role> Roles { get; set; } = [];

  /// <summary>
  /// 角色菜单关联
  /// </summary>
  //public ICollection<RoleMenu> RoleMenus { get; set; } = [];
}
