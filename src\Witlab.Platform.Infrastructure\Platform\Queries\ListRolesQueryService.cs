﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Roles.List;

namespace Witlab.Platform.Infrastructure.Platform.Queries;

/// <summary>
/// 列出角色查询服务实现
/// </summary>
public class ListRolesQueryService : IListRolesQueryService
{
  private readonly AppDbContext _dbContext;

  public ListRolesQueryService(AppDbContext dbContext)
  {
    _dbContext = dbContext;
  }

  public async Task<IEnumerable<RoleDTO>> ListAsync(int? skip, int? take)
  {
    IQueryable<Role> query = _dbContext.Roles.Include(r => r.RolePermissions).ThenInclude(rp => rp.Permission);

    if (skip.HasValue)
    {
      query = query.Skip(skip.Value);
    }

    if (take.HasValue)
    {
      query = query.Take(take.Value);
    }

    var roles = await query.ToListAsync();

    return roles.Select(role => new RoleDTO(
        role.Id,
        role.RoleName,
        role.RoleCode,
        role.Remark,
        role.State,
        role.Permissions?.Select(p => p.Code).ToList() ?? [],
        role.Created.DateTime,
        role.CreatedBy,
        role.LastModified.DateTime,
        role.LastModifiedBy
    ));
  }
}
