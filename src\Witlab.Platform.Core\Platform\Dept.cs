﻿using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 部门表
///</summary>
public class Dept : AuditableEntityBase<Guid>, IAggregateRoot
{
  private Dept()
  {
  }

  public Dept(string deptName, string deptCode)
  {
    Id = Guid.NewGuid();
    DeptCode = deptCode;
    DeptName = deptName;
    RegisterDomainEvent(new DeptCreatedEvent(Id, DeptCode, DeptName));
  }

  public Dept(Guid parentId, string deptName, string deptCode)
  {
    Id = Guid.NewGuid();
    ParentId = parentId;
    DeptCode = deptCode;
    DeptName = deptName;
    RegisterDomainEvent(new DeptCreatedEvent(Id, DeptCode, DeptName));
  }

  /// <summary>
  /// 排序
  /// </summary>
  public int OrderNum { get; set; } = 0;

  /// <summary>
  /// 状态
  /// </summary>
  public DeptState State { get; set; } = DeptState.Activate;

  /// <summary>
  /// 部门名称 
  ///</summary>
  public string DeptName { get; private set; } = string.Empty;
  /// <summary>
  /// 部门编码 
  ///</summary>
  public string DeptCode { get; private set; } = string.Empty;
  /// <summary>
  /// 负责人 
  ///</summary>
  public string? Leader { get; set; }
  /// <summary>
  /// 父级id 
  ///</summary>
  public Guid? ParentId { get; set; }

  public List<Dept>? Children { get; set; }

  /// <summary>
  /// 描述 
  ///</summary>
  public string? Remark { get; set; }

  //public ICollection<User> Users { get; set; } = new List<User>();
  public ICollection<UserDept> UserDepts { get; set; } = new List<UserDept>();
}
