﻿using Ardalis.Result;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Users.Register;

namespace Witlab.Platform.UnitTests.UseCases.Platform.Users;

public class RegisterHandlerTests
{
  private readonly Mock<IUserService> _mockUserService;
  private readonly Mock<IRoleService> _mockRoleService;
  private readonly RegisterHandler _handler;

  public RegisterHandlerTests()
  {
    _mockUserService = new Mock<IUserService>();
    _mockRoleService = new Mock<IRoleService>();
    _handler = new RegisterHandler(_mockUserService.Object, _mockRoleService.Object);
  }

  [Fact]
  public async Task Handle_WithValidData_ShouldRegisterUser()
  {
    // Arrange
    var command = new RegisterCommand(
        "testuser",
        "Password123!",
        "Test User",
        "<EMAIL>",
        null
    );

    var user = new User("testuser", "1q2w3E*", "Test User", "<EMAIL>");
    var userDto = new UserDTO(
        Guid.NewGuid(),
        "testuser",
        "Test User",
        "<EMAIL>",
        null,
        null,
        null,
        0,
        "Unknown",
        0,
        "Active",
        null,
        new List<string>(),
        new List<string>(),
        DateTime.UtcNow,
        null,
        null,
        null // Add this parameter to fix the error
    );

    _mockUserService.Setup(x => x.GetUserByNameAsync("testuser"))
        .ReturnsAsync(Result.NotFound("User not found"));

    _mockUserService.Setup(x => x.CreateUserAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<string>>(),
            It.IsAny<string>(),
            It.IsAny<long?>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<int?>(),
            It.IsAny<int?>(),
            It.IsAny<string>()))
        .ReturnsAsync(Result.Success(user));

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    Assert.True(result.IsSuccess);
    _mockUserService.Verify(x => x.CreateUserAsync(
        "testuser",
        "Password123!",
        "Test User",
        null, // Add this parameter for depts
        new List<string>(), // Add this parameter for roles
        "<EMAIL>",
        null,
        null,
        null,
        null,
        null,
        null), Times.Once);
  }

  [Fact]
  public async Task Handle_WithExistingUsername_ShouldReturnError()
  {
    // Arrange
    var command = new RegisterCommand(
        "existinguser",
        "Password123!",
        "Existing User",
        "<EMAIL>",
        null
    );

    var existingUser = new User("existinguser", "Password123!", "Existing User", "<EMAIL>");

    _mockUserService.Setup(x => x.GetUserByNameAsync("existinguser"))
        .ReturnsAsync(Result.Success(existingUser));

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    Assert.False(result.IsSuccess);
    Assert.Contains("用户名已存在", result.Errors);
    _mockUserService.Verify(x => x.CreateUserAsync(
        It.IsAny<string>(),
        It.IsAny<string>(),
        It.IsAny<string>(),
        It.IsAny<List<string>>(),
        It.IsAny<List<string>>(),
        It.IsAny<string>(),
        It.IsAny<long?>(),
        It.IsAny<string>(),
        It.IsAny<string>(),
        It.IsAny<int?>(),
        It.IsAny<int?>(),
        It.IsAny<string>()));
  }
}
