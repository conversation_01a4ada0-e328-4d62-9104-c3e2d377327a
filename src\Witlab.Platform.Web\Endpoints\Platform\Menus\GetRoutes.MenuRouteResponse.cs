﻿namespace Witlab.Platform.Web.Endpoints.Platform.Menus;

/// <summary>
/// 菜单路由响应
/// </summary>
public class MenuRouteResponse
{
  /// <summary>
  /// 菜单ID
  /// </summary>
  public Guid Id { get; set; }

  /// <summary>
  /// 菜单名称
  /// </summary>
  public string Name { get; set; } = string.Empty;

  /// <summary>
  /// 路由路径
  /// </summary>
  public string Path { get; set; } = string.Empty;

  /// <summary>
  /// 菜单类型: 'menu' | 'catalog' | 'button' | 'embedded' | 'link'
  /// </summary>
  public string Type { get; set; } = "menu";

  /// <summary>
  /// 状态(1:启用, 0:禁用)
  /// </summary>
  public int Status { get; set; } = 1;

  /// <summary>
  /// 父级ID
  /// </summary>
  public Guid? Pid { get; set; }

  /// <summary>
  /// 组件路径
  /// </summary>
  public string? Component { get; set; }

  /// <summary>
  /// 权限编码
  /// </summary>
  public string? AuthCode { get; set; }

  /// <summary>
  /// 重定向路径
  /// </summary>
  public string? Redirect { get; set; }

  /// <summary>
  /// 元数据
  /// </summary>
  public MenuMetaResponse Meta { get; set; } = new MenuMetaResponse();

  /// <summary>
  /// 子菜单
  /// </summary>
  public List<MenuRouteResponse> Children { get; set; } = [];
}

/// <summary>
/// 菜单元数据响应
/// </summary>
public class MenuMetaResponse
{
  /// <summary>
  /// 图标
  /// </summary>
  public string? Icon { get; set; }

  /// <summary>
  /// 激活时显示的图标
  /// </summary>
  public string? ActiveIcon { get; set; }

  /// <summary>
  /// 标题
  /// </summary>
  public string? Title { get; set; }

  /// <summary>
  /// 排序
  /// </summary>
  public int Order { get; set; }

  /// <summary>
  /// 是否固定标签
  /// </summary>
  public bool AffixTab { get; set; }

  /// <summary>
  /// 在标签栏固定的顺序
  /// </summary>
  public int? AffixTabOrder { get; set; }

  /// <summary>
  /// 徽标文本
  /// </summary>
  public string? Badge { get; set; }

  /// <summary>
  /// 徽标类型: 'dot' | 'normal'
  /// </summary>
  public string? BadgeType { get; set; }

  /// <summary>
  /// 徽标变体
  /// </summary>
  public string? BadgeVariants { get; set; }

  /// <summary>
  /// iframe源地址(用于嵌入式页面)
  /// </summary>
  public string? IframeSrc { get; set; }

  /// <summary>
  /// 外部链接(用于链接类型)
  /// </summary>
  public string? Link { get; set; }

  /// <summary>
  /// 是否在新窗口打开
  /// </summary>
  public bool? OpenInNewWindow { get; set; }

  /// <summary>
  /// 是否缓存页面
  /// </summary>
  public bool? KeepAlive { get; set; }

  /// <summary>
  /// 在菜单中隐藏
  /// </summary>
  public bool? HideInMenu { get; set; }

  /// <summary>
  /// 在标签栏中隐藏
  /// </summary>
  public bool? HideInTab { get; set; }

  /// <summary>
  /// 在面包屑中隐藏
  /// </summary>
  public bool? HideInBreadcrumb { get; set; }

  /// <summary>
  /// 在菜单中隐藏下级
  /// </summary>
  public bool? HideChildrenInMenu { get; set; }

  /// <summary>
  /// 额外的路由参数
  /// </summary>
  public Dictionary<string, string>? Query { get; set; }

  /// <summary>
  /// 作为路由时，需要激活的菜单的Path
  /// </summary>
  public string? ActivePath { get; set; }

  /// <summary>
  /// 同一个路由最大打开的标签数
  /// </summary>
  public int? MaxNumofOpenTab { get; set; }

  /// <summary>
  /// 无需基础布局
  /// </summary>
  public bool? NoBasicLayout { get; set; }

  /// <summary>
  /// 路由权限控制，roles code
  /// </summary>
  public List<string>? Authority { get; set; }

}
