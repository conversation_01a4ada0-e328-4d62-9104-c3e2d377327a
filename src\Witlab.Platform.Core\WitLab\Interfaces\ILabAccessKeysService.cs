﻿namespace Witlab.Platform.Core.WitLab.Interfaces;
public interface ILabAccessKeysService
{
  Task<Result<LabAccessKeys>> GetAccessKeys(string userName, string deptCode, string? roleCode = null, CancellationToken cancellationToken = default);

  Task<Result<LabAccessKeys>> CreateKeysAsync(string userName, string accessKey, string secretKey, string deptCode, string? roleCode = null, CancellationToken cancellationToken = default);

  Task<Result> DeleteKeysAsync(Guid guid, CancellationToken cancellationToken = default);
}
