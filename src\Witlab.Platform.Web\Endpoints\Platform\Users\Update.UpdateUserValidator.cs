﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 更新用户请求验证器
/// </summary>
public class UpdateUserValidator : Validator<UpdateUserRequest>
{
  public UpdateUserValidator()
  {
    RuleFor(x => x.UserId)
        .NotEmpty().WithMessage("用户ID不能为空");

    RuleFor(x => x.FullName)
        .NotEmpty().WithMessage("姓名不能为空")
        .MinimumLength(2).WithMessage("姓名长度不能少于2个字符")
        .MaximumLength(50).WithMessage("姓名长度不能超过50个字符");

    RuleFor(x => x.Email)
        .EmailAddress().WithMessage("邮箱格式不正确")
        .When(x => !string.IsNullOrEmpty(x.Email));

    RuleFor(x => x.SexValue)
        .InclusiveBetween(0, 2).WithMessage("性别值必须在0-2之间")
        .When(x => x.SexValue.HasValue);

    RuleFor(x => x.Address)
        .MaximumLength(200).WithMessage("地址长度不能超过200个字符")
        .When(x => !string.IsNullOrEmpty(x.Address));

    RuleFor(x => x.Remark)
        .MaximumLength(500).WithMessage("备注长度不能超过500个字符")
        .When(x => !string.IsNullOrEmpty(x.Remark));
  }
}
