﻿using Ardalis.Result;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Sessions;

/// <summary>
/// 获取会话统计信息查询
/// </summary>
public record GetSessionStatisticsQuery() : IQuery<Result<SessionStatisticsDTO>>;

/// <summary>
/// 获取会话统计信息查询处理器
/// </summary>
public class GetSessionStatisticsQueryHandler : I<PERSON><PERSON>y<PERSON>andler<GetSessionStatisticsQuery, Result<SessionStatisticsDTO>>
{
  private readonly IUserSessionService _userSessionService;

  public GetSessionStatisticsQueryHandler(IUserSessionService userSessionService)
  {
    _userSessionService = userSessionService;
  }

  public async Task<Result<SessionStatisticsDTO>> Handle(GetSessionStatisticsQuery request, CancellationToken cancellationToken)
  {
    var result = await _userSessionService.GetSessionStatisticsAsync();
    
    if (!result.IsSuccess)
    {
      return Result<SessionStatisticsDTO>.Error(new ErrorList(result.Errors));
    }

    var statistics = result.Value;
    var statisticsDTO = new SessionStatisticsDTO(
      statistics.TotalOnlineUsers,
      statistics.TotalActiveSessions,
      statistics.WebSessions,
      statistics.MobileSessions,
      statistics.DesktopSessions,
      statistics.ApiSessions,
      statistics.TodayNewSessions,
      statistics.StatisticsTime
    );

    return Result<SessionStatisticsDTO>.Success(statisticsDTO);
  }
}
