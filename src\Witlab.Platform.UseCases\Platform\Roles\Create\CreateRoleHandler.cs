﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.Create;

/// <summary>
/// 创建角色命令处理器
/// </summary>
public class CreateRoleHandler : ICommandHandler<CreateRoleCommand, Result<RoleDTO>>
{
  private readonly IRoleService _roleService;

  public CreateRoleHandler(IRoleService roleService)
  {
    _roleService = roleService;
  }

  public async Task<Result<RoleDTO>> Handle(CreateRoleCommand request, CancellationToken cancellationToken)
  {
    var result = await _roleService.CreateRoleAsync(
        request.RoleName!,
        request.RoleCode!,
        request.Permissions,
        request.Remark,
        request.Status
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var role = result.Value;
    return Result.Success(MapToDto(role));
  }

  private static RoleDTO MapToDto(Role role)
  {
    return new RoleDTO(
        role.Id,
        role.RoleName,
        role.RoleCode,
        role.Remark,
        role.State,
        Array.Empty<string>(),
        role.Created.DateTime, // Convert DateTimeOffset to DateTime
        role.CreatedBy,
        role.LastModified.DateTime, // Convert DateTimeOffset to DateTime
        role.LastModifiedBy
    );
  }
}
