﻿using Witlab.Platform.UseCases.Platform.Roles.AssignPermissions;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 分配权限给角色
/// </summary>
public class AssignPermissions(IMediator _mediator) : Endpoint<AssignPermissionsRequest>
{
  public override void Configure()
  {
    Post(AssignPermissionsRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "分配权限给角色";
      s.Description = "为指定角色分配一组权限";
    });
  }

  public override async Task HandleAsync(AssignPermissionsRequest request, CancellationToken cancellationToken)
  {
    var command = new AssignPermissionsCommand(
        request.RoleId,
        request.PermissionIds
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
