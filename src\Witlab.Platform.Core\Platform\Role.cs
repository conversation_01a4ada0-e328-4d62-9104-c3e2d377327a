﻿using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 角色表
/// </summary>
public class Role : AuditableEntityBase<Guid>, IAggregateRoot
{
  private Role()
  {
  }

  public Role(string roleName, string roleCode)
  {
    RoleName = roleName;
    RoleCode = roleCode;
    RegisterDomainEvent(new RoleCreatedEvent(Id, RoleCode, RoleName));
  }

  /// <summary>
  /// 角色名
  /// </summary>
  public string RoleName { get; set; } = string.Empty;

  /// <summary>
  /// 角色编码
  ///</summary>
  public string RoleCode { get; set; } = string.Empty;

  /// <summary>
  /// 描述
  ///</summary>
  public string? Remark { get; set; }
  /// <summary>
  /// 角色数据范围
  ///</summary>
  //public RoleDataScope DataScope { get; set; } = RoleDataScope.All;

  /// <summary>
  /// 状态
  /// </summary>
  public bool State { get; set; } = true;

  /// <summary>
  /// 用户列表
  /// </summary>
  //public ICollection<User> Users { get; set; } = new List<User>();

  /// <summary>
  /// 用户角色关联
  /// </summary>
  public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

  /// <summary>
  /// 角色权限关联
  /// </summary>
  public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();

  /// <summary>
  /// 权限列表（导航属性）
  /// </summary>
  public ICollection<Permission> Permissions { get; set; } = new List<Permission>();
}
