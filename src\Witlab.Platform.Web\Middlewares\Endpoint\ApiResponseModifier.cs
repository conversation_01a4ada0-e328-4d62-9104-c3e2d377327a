﻿using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.WebUtilities;

namespace Witlab.Platform.Web.Middlewares.Endpoint;

public static class ApiResponseModifier
{
  public static async Task ResponseModifiy(HttpContext ctx, object? content)
  {
    var httpCtx = ctx;
    var statusCode = ctx.Response.StatusCode;

    // 获取异常或错误对象(可自定义)
    object? error = null;

    // 一般情况下没有异常，某些中间件会在Items或Features里放异常
    var feature = httpCtx.Features.Get<IExceptionHandlerFeature>();
    if (feature?.Error != null || httpCtx.Items.ContainsKey("Error"))
    {
      //error = context.ExceptionDispatchInfo?.SourceException.Message ?? feature?.Error.Message ?? httpCtx.Items["Error"];

      error = feature?.Error.Message ?? httpCtx.Items["Error"];
    }

    var apiResponse = new ApiResponse<object>
    {
      Code = statusCode,
      Data = error == null ? content : default,
      Error = error,
      Message = ReasonPhrases.GetReasonPhrase(statusCode)
    };

    await httpCtx.Response.Body.FlushAsync();

    var json = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions()
    {
      PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
      DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    });

    await httpCtx.Response.BodyWriter.WriteAsync(System.Text.Encoding.UTF8.GetBytes(json));
  }
}
