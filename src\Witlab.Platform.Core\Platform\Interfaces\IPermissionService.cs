﻿namespace Witlab.Platform.Core.Platform.Interfaces;

/// <summary>
/// 权限领域服务接口
/// </summary>
public interface IPermissionService
{
  /// <summary>
  /// 创建权限
  /// </summary>
  /// <param name="code">权限编码</param>
  /// <param name="name">权限名称</param>
  /// <param name="type">权限类型</param>
  /// <param name="description">权限描述</param>
  /// <param name="menuId">关联的菜单ID</param>
  /// <returns>创建的权限</returns>
  Task<Result<Permission>> CreatePermissionAsync(string code, string name, PermissionType type, string? description = null, Guid? menuId = null);

  /// <summary>
  /// 更新权限
  /// </summary>
  /// <param name="permissionId">权限ID</param>
  /// <param name="code">权限编码</param>
  /// <param name="name">权限名称</param>
  /// <param name="type">权限类型</param>
  /// <param name="description">权限描述</param>
  /// <param name="menuId">关联的菜单ID</param>
  /// <returns>更新结果</returns>
  Task<Result<Permission>> UpdatePermissionAsync(Guid permissionId, string code, string name, PermissionType type, string? description, Guid? menuId);

  /// <summary>
  /// 删除权限
  /// </summary>
  /// <param name="permissionId">权限ID</param>
  /// <returns>删除结果</returns>
  Task<Result> DeletePermissionAsync(Guid permissionId);

  /// <summary>
  /// 获取权限
  /// </summary>
  /// <param name="permissionId">权限ID</param>
  /// <returns>权限</returns>
  Task<Result<Permission>> GetPermissionAsync(Guid permissionId);

  /// <summary>
  /// 根据权限编码获取权限
  /// </summary>
  /// <param name="code">权限编码</param>
  /// <returns>权限</returns>
  Task<Result<Permission>> GetPermissionByCodeAsync(string code);

  /// <summary>
  /// 根据菜单ID获取权限列表
  /// </summary>
  /// <param name="menuId">菜单ID</param>
  /// <returns>权限列表</returns>
  Task<Result<List<Permission>>> GetPermissionsByMenuIdAsync(Guid menuId);

  /// <summary>
  /// 根据权限类型获取权限列表
  /// </summary>
  /// <param name="type">权限类型</param>
  /// <returns>权限列表</returns>
  Task<Result<List<Permission>>> GetPermissionsByTypeAsync(PermissionType type);

  /// <summary>
  /// 验证用户是否拥有指定权限
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="permissionCode">权限编码</param>
  /// <returns>验证结果</returns>
  Task<Result<bool>> ValidateUserPermissionAsync(Guid userId, string permissionCode);

  /// <summary>
  /// 获取用户的所有权限
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>权限列表</returns>
  Task<Result<List<Permission>>> GetUserPermissionsAsync(Guid userId);

  /// <summary>
  /// 启用权限
  /// </summary>
  /// <param name="permissionId">权限ID</param>
  /// <returns>启用结果</returns>
  Task<Result> EnablePermissionAsync(Guid permissionId);

  /// <summary>
  /// 禁用权限
  /// </summary>
  /// <param name="permissionId">权限ID</param>
  /// <returns>禁用结果</returns>
  Task<Result> DisablePermissionAsync(Guid permissionId);
}
