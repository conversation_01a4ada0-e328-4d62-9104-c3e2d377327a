﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Update;

/// <summary>
/// 更新用户命令处理器
/// </summary>
public class UpdateUserHandler : ICommandHandler<UpdateUserCommand, Result<UserDTO>>
{
  private readonly IUserService _userService;

  public UpdateUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result<UserDTO>> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
  {
    var result = await _userService.UpdateUserAsync(
        request.UserId,
        request.FullName,
        request.Depts,
        request.Roles,
        request.Email,
        request.Phone,
        request.Address,
        request.Icon,
        request.SexValue,
        request.StateValue,
        request.Remark
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var user = result.Value;
    return Result.Success(MapToDto(user));
  }

  private static UserDTO MapToDto(User user)
  {
    return new UserDTO(
        user.Id,
        user.UserName,
        user.FullName,
        user.Email,
        user.Phone,
        user.Address,
        user.Icon,
        user.Sex.Value,
        user.Sex.Name,
        user.State.Value,
        user.State.Name,
        user.Remark,
        user.UserRoles.Select(l => l.Role).Select(r => r.RoleCode).ToList() ?? [],
        user.UserDepts.Select(l => l.Dept).Select(r => r.DeptCode).ToList() ?? [],
        user.Created.DateTime,
        user.CreatedBy,
        user.LastModified.DateTime,
        user.LastModifiedBy
    );
  }
}
