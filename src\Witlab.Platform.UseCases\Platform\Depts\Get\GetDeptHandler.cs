﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Depts.Get;

/// <summary>
/// 获取部门查询处理器
/// </summary>
public class GetDeptHandler : IQueryHandler<GetDeptQuery, Result<DeptDTO>>
{
  private readonly IDeptService _deptService;

  public GetDeptHandler(IDeptService deptService)
  {
    _deptService = deptService;
  }

  public async Task<Result<DeptDTO>> Handle(GetDeptQuery request, CancellationToken cancellationToken)
  {
    var result = await _deptService.GetDeptAsync(request.DeptId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var dept = result.Value;
    return Result.Success(MapToDto(dept));
  }

  private static DeptDTO MapToDto(Dept dept)
  {
    return new DeptDTO(
        dept.Id,
        dept.DeptName,
        dept.DeptCode,
        dept.ParentId,
        dept.Leader,
        dept.OrderNum,
        dept.State.Value,
        dept.State.Name,
        dept.Remark,
        dept.Created.DateTime,
        dept.CreatedBy,
        dept.LastModified.DateTime,
        dept.LastModifiedBy
    );
  }
}
