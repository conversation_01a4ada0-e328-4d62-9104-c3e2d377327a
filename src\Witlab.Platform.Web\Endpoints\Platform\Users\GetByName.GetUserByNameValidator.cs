﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据用户名获取用户请求验证器
/// </summary>
public class GetUserByNameValidator : Validator<GetUserByNameRequest>
{
  public GetUserByNameValidator()
  {
    RuleFor(x => x.UserName)
        .NotEmpty().WithMessage("用户名不能为空")
        .MinimumLength(3).WithMessage("用户名长度不能少于3个字符")
        .MaximumLength(50).WithMessage("用户名长度不能超过50个字符");
  }
}
