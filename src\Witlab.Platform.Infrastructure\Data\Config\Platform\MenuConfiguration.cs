﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class MenuConfiguration : IEntityTypeConfiguration<Menu>
{
  public void Configure(EntityTypeBuilder<Menu> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<Menu, Guid>();

    builder.Property(p => p.Component)
        .HasMaxLength(500);

    // 基本字段
    builder.Property(p => p.MenuName)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.RouterName)
        .HasMaxLength(50);

    builder.Property(p => p.Router)
        .HasMaxLength(200);

    builder.Property(p => p.Redirect)
        .HasMaxLength(500);

    builder.Property(p => p.MenuIcon)
        .HasMaxLength(100);

    builder.Property(p => p.ActiveIcon)
        .HasMaxLength(50);

    builder.Property(p => p.ActivePath)
        .HasMaxLength(100);

    // 徽标相关
    builder.Property(p => p.Badge)
        .HasMaxLength(50);

    builder.Property(p => p.BadgeType)
        .HasMaxLength(20);

    builder.Property(p => p.BadgeVariants)
        .HasMaxLength(20);

    // 链接相关
    builder.Property(p => p.IframeSrc)
        .HasMaxLength(500);

    builder.Property(p => p.Link)
        .HasMaxLength(500);

    builder.Property(p => p.Remark)
        .HasMaxLength(500);

  builder.Property(p => p.Query)
      .HasConversion(
          v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
          v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, JsonSerializerOptions.Default))
      .Metadata.SetValueComparer(comparer: new ValueComparer<Dictionary<string, string>>(
          (c1, c2) => JsonSerializer.Serialize(c1, JsonSerializerOptions.Default) == JsonSerializer.Serialize(c2, JsonSerializerOptions.Default),
          c => c == null ? 0 : JsonSerializer.Serialize(c, JsonSerializerOptions.Default).GetHashCode(),
          c => c ?? new Dictionary<string, string>()));

    builder.Property(p => p.Title)
        .HasMaxLength(100);

    // 配置MenuType SmartEnum转换
    builder.Property(p => p.MenuType)
        .HasConversion(
            v => v.Value,
            v => MenuType.FromValue(v)
    );

    // 配置MenuState SmartEnum转换
    builder.Property(p => p.State)
        .HasConversion(
            v => v.Value,
            v => MenuState.FromValue(v));

    // 不再使用外键约束，而是在应用程序代码中处理父子关系
    // 配置Children集合，但不设置外键约束
    builder.HasMany(e => e.Children)
        .WithOne()
        .HasForeignKey(e => e.ParentId)
        .IsRequired(false)  // 允许ParentId为null
        .OnDelete(DeleteBehavior.Restrict);

    // 忽略外键约束检查
    builder.HasIndex(e => e.ParentId)
        .HasDatabaseName("IX_Menus_ParentId");

    // 确保ParentId可以为null或Guid.Empty
    builder.Property(e => e.ParentId)
        .IsRequired(false);

    builder.ToTable("Menus");
  }
}
