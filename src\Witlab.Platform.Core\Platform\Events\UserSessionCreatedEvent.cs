namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 用户会话创建事件
/// </summary>
public class UserSessionCreatedEvent : DomainEventBase
{
  /// <summary>
  /// 会话ID
  /// </summary>
  public Guid SessionId { get; }

  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; }

  /// <summary>
  /// IP地址
  /// </summary>
  public string IpAddress { get; }

  public UserSessionCreatedEvent(Guid sessionId, Guid userId, string userName, string ipAddress)
  {
    SessionId = sessionId;
    UserId = userId;
    UserName = userName;
    IpAddress = ipAddress;
  }
}
