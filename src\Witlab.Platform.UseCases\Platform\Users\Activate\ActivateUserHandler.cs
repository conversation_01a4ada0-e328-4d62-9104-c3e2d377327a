﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Activate;

/// <summary>
/// 激活用户命令处理器
/// </summary>
public class ActivateUserHandler : ICommandHandler<ActivateUserCommand, Result>
{
  private readonly IUserService _userService;

  public ActivateUserHandler(IUserService userService)
  {
    _userService = userService;
  }

  public async Task<Result> Handle(ActivateUserCommand request, CancellationToken cancellationToken)
  {
    return await _userService.ActivateUserAsync(request.UserId);
  }
}
