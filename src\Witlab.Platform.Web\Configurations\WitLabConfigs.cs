﻿using System.Net;
using Witlab.Platform.Infrastructure.WitLab.Configurations;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;
using Witlab.Platform.Infrastructure.WitLab.Services;

namespace Witlab.Platform.Web.Configurations;

public static class WitLabConfigs
{
  public static IServiceCollection AddWitLabConfigs(this IServiceCollection services, IConfiguration configuration, Microsoft.Extensions.Logging.ILogger logger, WebApplicationBuilder builder)
  {
    var witLabConfig = new WitLabConfiguration();
    configuration.GetSection("WitLab").Bind(witLabConfig);

    if (string.IsNullOrEmpty(witLabConfig.WITLAB_SERVER_HOST))
    {
      logger.LogError("WitLab server host is not configured.");
      throw new ArgumentNullException(nameof(witLabConfig.WITLAB_SERVER_HOST), "WitLab server host is not configured.");
    }

    if (string.IsNullOrEmpty(witLabConfig.WITLAB_SERVER_SITE))
    {
      logger.LogError("WitLab server site is not configured.");
      throw new ArgumentNullException(nameof(witLabConfig.WITLAB_SERVER_SITE), "WitLab server site is not configured.");
    }

    if (string.IsNullOrEmpty(witLabConfig.WITLAB_ADMIN_API_ACCESS_KEY))
    {
      logger.LogError("WitLab admin API access key is not configured.");
      throw new ArgumentNullException(nameof(witLabConfig.WITLAB_ADMIN_API_ACCESS_KEY), "WitLab admin api access key is not configured.");
    }

    if (string.IsNullOrEmpty(witLabConfig.WITLAB_ADMIN_API_SECRET_KEY))
    {
      logger.LogError("WitLab admin API secret key is not configured.");
      throw new ArgumentNullException(nameof(witLabConfig.WITLAB_ADMIN_API_SECRET_KEY), "WitLab admin api secret key is not configured.");
    }

    services.Configure<WitLabConfiguration>(configuration.GetSection("WitLab"));

    // see more detail at https://www.nuget.org/packages/Microsoft.Extensions.Http.Resilience/
    services.AddHttpClient("WitLab", (sp, client) =>
    {
      client.Timeout = TimeSpan.FromSeconds(300);
    })
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
      AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
    });

    logger.LogInformation("{Project} were configured", "WitLab configuration");
    logger.LogInformation("Connect to WitLab Server: {0}", $"{witLabConfig.WITLAB_SERVER_HOST}/{witLabConfig.WITLAB_SERVER_SITE}");

    services.AddScoped<IWitLabProxyService, WitLabProxyService>();
    services.AddScoped<IWitLabSyncService, WitLabSyncService>();
    services.AddScoped<IWitLabFileService, WitLabFileService>();
    services.AddScoped<IWitLabKeyProvider, WitLabKeyProvider>();

    return services;
  }
}
