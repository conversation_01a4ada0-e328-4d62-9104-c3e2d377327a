﻿using Witlab.Platform.UseCases.Platform.Roles.Get;

namespace Witlab.Platform.Web.Endpoints.Platform.Roles;

/// <summary>
/// 根据ID获取角色
/// </summary>
public class GetById(IMediator _mediator) : Endpoint<GetRoleByIdRequest, RoleRecord>
{
  public override void Configure()
  {
    Get(GetRoleByIdRequest.Route);
    Description(x => x.AutoTagOverride("Role"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "根据ID获取角色";
      s.Description = "根据角色ID获取角色详细信息";
    });
  }

  public override async Task HandleAsync(GetRoleByIdRequest request, CancellationToken cancellationToken)
  {
    var query = new GetRoleQuery(request.RoleId);

    var result = await _mediator.Send(query, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new RoleRecord()
      {
        Id = dto.Id.ToString(),
        RoleName = dto.RoleName,
        RoleCode = dto.RoleCode,
        Remark = dto.Remark,
        Status = dto.State ? 1 : 0,
        Permissions = dto.Permissions.ToList(),
        CreatedOnUtc = dto.CreatedOnUtc,
        CreatedBy = dto.CreatedBy,
        LastModifiedOnUtc = dto.LastModifiedOnUtc,
        LastModifiedBy = dto.LastModifiedBy
      };
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
