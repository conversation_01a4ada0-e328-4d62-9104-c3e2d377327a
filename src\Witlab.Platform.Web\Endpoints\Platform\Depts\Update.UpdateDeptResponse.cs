﻿namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

public class UpdateDeptResponse
{
  public UpdateDeptResponse(
      Guid id,
      string deptName,
      string deptCode,
      Guid? parentId,
      string? leader,
      int orderNum,
      int stateValue,
      string stateName,
      string? remark)
  {
    Id = id;
    DeptName = deptName;
    DeptCode = deptCode;
    ParentId = parentId;
    Leader = leader;
    OrderNum = orderNum;
    StateValue = stateValue;
    StateName = stateName;
    Remark = remark;
  }

  public Guid Id { get; set; }
  public string DeptName { get; set; }
  public string DeptCode { get; set; }
  public Guid? ParentId { get; set; }
  public string? Leader { get; set; }
  public int OrderNum { get; set; }
  public int StateValue { get; set; }
  public string StateName { get; set; }
  public string? Remark { get; set; }
}
