﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.UseCases.Platform.Permissions.List;

/// <summary>
/// 列出权限查询服务接口
/// </summary>
public interface IListPermissionsQueryService
{
  /// <summary>
  /// 列出权限
  /// </summary>
  /// <param name="skip">跳过的记录数</param>
  /// <param name="take">获取的记录数</param>
  /// <param name="type">权限类型</param>
  /// <returns>权限列表</returns>
  Task<IEnumerable<PermissionDTO>> ListAsync(int? skip, int? take, PermissionType? type = null);
}
