﻿namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 权限实体
/// </summary>
public class Permission : AuditableEntityBase<Guid>, IAggregateRoot
{
  private Permission()
  {
    // For EF Core
    Type = PermissionType.Element; // 默认值，避免null异常
  }

  public Permission(string code, string name, PermissionType type)
  {
    Code = Guard.Against.NullOrEmpty(code, nameof(code));
    Name = Guard.Against.NullOrEmpty(name, nameof(name));
    Type = type;
    IsEnabled = true;
  }

  /// <summary>
  /// 权限编码，如"user:create"、"user:update"等
  /// </summary>
  public string Code { get; private set; } = string.Empty;

  /// <summary>
  /// 权限名称，如"创建用户"、"更新用户"等
  /// </summary>
  public string Name { get; private set; } = string.Empty;

  /// <summary>
  /// 权限描述
  /// </summary>
  public string? Description { get; set; }

  /// <summary>
  /// 权限类型
  /// </summary>
  public PermissionType Type { get; set; }

  /// <summary>
  /// 关联的菜单
  /// </summary>
  public Menu? Menu { get; set; }

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool IsEnabled { get; set; }

  /// <summary>
  /// 更新权限信息
  /// </summary>
  public void Update(string code, string name, string? description, PermissionType type)
  {
    Code = Guard.Against.NullOrEmpty(code, nameof(code));
    Name = Guard.Against.NullOrEmpty(name, nameof(name));
    Description = description;
    Type = type;
  }

  /// <summary>
  /// 启用权限
  /// </summary>
  public void Enable()
  {
    IsEnabled = true;
  }

  /// <summary>
  /// 禁用权限
  /// </summary>
  public void Disable()
  {
    IsEnabled = false;
  }

  /// <summary>
  /// 角色权限关联
  /// </summary>
  public ICollection<RolePermission> RolePermissions { get; set; } = [];
  public ICollection<Role> Roles { get; set; } = [];
}
