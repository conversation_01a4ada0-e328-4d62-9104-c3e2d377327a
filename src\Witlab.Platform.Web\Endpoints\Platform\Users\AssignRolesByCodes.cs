using Witlab.Platform.UseCases.Platform.Users.AssignRolesByCodes;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 根据角色编码分配角色给用户
/// </summary>
[RequirePermission("user:assign-roles")]
public class AssignRolesByCodes(IMediator _mediator) : Endpoint<AssignRolesToUserByCodesRequest>
{
  public override void Configure()
  {
    Post(AssignRolesToUserByCodesRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    Summary(s =>
    {
      s.Summary = "根据角色编码分配角色给用户";
      s.Description = "为指定用户分配一组角色（使用角色编码）";
    });
  }

  public override async Task HandleAsync(AssignRolesToUserByCodesRequest request, CancellationToken cancellationToken)
  {
    var command = new AssignRolesToUserByCodesCommand(
        request.UserId,
        request.RoleCodes
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync(cancellationToken);
      return;
    }

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    await SendErrorsAsync(cancellation: cancellationToken);
  }
}
