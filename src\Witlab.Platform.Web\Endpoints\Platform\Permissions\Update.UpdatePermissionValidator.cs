﻿using FluentValidation;

namespace Witlab.Platform.Web.Endpoints.Platform.Permissions;

/// <summary>
/// 更新权限请求验证器
/// </summary>
public class UpdatePermissionValidator : Validator<UpdatePermissionRequest>
{
  public UpdatePermissionValidator()
  {
    RuleFor(x => x.PermissionId)
        .NotEmpty().WithMessage("权限ID不能为空");

    RuleFor(x => x.Code)
        .NotEmpty().WithMessage("权限编码不能为空")
        .MinimumLength(2).WithMessage("权限编码长度不能少于2个字符")
        .MaximumLength(100).WithMessage("权限编码长度不能超过100个字符");

    RuleFor(x => x.Name)
        .NotEmpty().WithMessage("权限名称不能为空")
        .MinimumLength(2).WithMessage("权限名称长度不能少于2个字符")
        .MaximumLength(100).WithMessage("权限名称长度不能超过100个字符");

    RuleFor(x => x.TypeValue)
        .InclusiveBetween(0, 3).WithMessage("权限类型值必须在0-3之间");

    RuleFor(x => x.Description)
        .MaximumLength(500).WithMessage("描述长度不能超过500个字符")
        .When(x => !string.IsNullOrEmpty(x.Description));
  }
}
