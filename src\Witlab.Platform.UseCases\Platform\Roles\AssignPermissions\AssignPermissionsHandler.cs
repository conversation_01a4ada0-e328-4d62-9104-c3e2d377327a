﻿using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Roles.AssignPermissions;

/// <summary>
/// 分配权限给角色命令处理器
/// </summary>
public class AssignPermissionsHandler : ICommandHandler<AssignPermissionsCommand, Result>
{
  private readonly IRoleService _roleService;

  public AssignPermissionsHandler(IRoleService roleService)
  {
    _roleService = roleService;
  }

  public async Task<Result> Handle(AssignPermissionsCommand request, CancellationToken cancellationToken)
  {
    return await _roleService.AssignPermissionsToRoleAsync(request.RoleId, request.PermissionIds);
  }
}
