﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Depts.GetTree;

/// <summary>
/// 获取部门树查询处理器
/// </summary>
public class GetDeptTreeHandler : I<PERSON>ueryHandler<GetDeptTreeQuery, Result<List<DeptDTO>>>
{
  private readonly IDeptService _deptService;

  public GetDeptTreeHandler(IDeptService deptService)
  {
    _deptService = deptService;
  }

  public async Task<Result<List<DeptDTO>>> Handle(GetDeptTreeQuery request, CancellationToken cancellationToken)
  {
    var result = await _deptService.GetDeptTreeAsync(request.ParentId);

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var depts = result.Value;
    return Result.Success(depts.Select(MapToDto).ToList());
  }

  private static DeptDTO MapToDto(Dept dept)
  {
    return new DeptDTO(
        dept.Id,
        dept.DeptName,
        dept.DeptCode,
        dept.ParentId,
        dept.Leader,
        dept.OrderNum,
        dept.State.Value,
        dept.State.Name,
        dept.Remark,
        dept.Created.DateTime,
        dept.CreatedBy,
        dept.LastModified.DateTime,
        dept.LastModifiedBy
    );
  }
}
