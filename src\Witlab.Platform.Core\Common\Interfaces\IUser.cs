﻿namespace Witlab.Platform.Core.Common.Interfaces;

/// <summary>
/// 当前用户接口
/// </summary>
public interface IUser
{
  /// <summary>
  /// 用户ID
  /// </summary>
  string? Id { get; }

  /// <summary>
  /// 用户名
  /// </summary>
  string? UserName { get; }

  /// <summary>
  /// 角色列表
  /// </summary>
  IEnumerable<string> Roles { get; }

  /// <summary>
  /// 当前登录的部门编码
  /// </summary>
  string? DeptCode { get; }

  /// <summary>
  /// 当前登录的角色编码
  /// </summary>
  string? RoleCode { get; }

  /// <summary>
  /// 判断是否拥有指定角色
  /// </summary>
  /// <param name="role">角色</param>
  /// <returns>是否拥有</returns>
  bool IsInRole(string role);
}
